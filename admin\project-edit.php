<?php
require_once __DIR__ . '/../includes/functions.php';
requireLogin();

$pageTitle = 'Edit Project';

// Get project ID
$projectId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
if (!$projectId) {
    header('Location: projects.php');
    exit();
}

// Get project data
$database = new Database();
$conn = $database->getConnection();
$stmt = $conn->prepare("SELECT * FROM projects WHERE id = ?");
$stmt->execute([$projectId]);
$project = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$project) {
    header('Location: projects.php');
    exit();
}

$message = '';
$messageType = '';

if ($_POST) {
    $title = sanitizeInput($_POST['title']);
    $description = sanitizeInput($_POST['description']);
    $location = sanitizeInput($_POST['location']);
    $client = sanitizeInput($_POST['client']);
    $status = sanitizeInput($_POST['status']);
    $startDate = $_POST['start_date'] ?: null;
    $endDate = $_POST['end_date'] ?: null;
    
    if ($title && $description && $location && $status) {
        // Handle featured image upload
        $featuredImage = $project['featured_image']; // Keep existing image
        if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
            $uploadResult = uploadFile($_FILES['featured_image'], '../uploads/');
            if ($uploadResult['success']) {
                // Delete old image if exists
                if ($project['featured_image'] && file_exists('../' . $project['featured_image'])) {
                    unlink('../' . $project['featured_image']);
                }
                $featuredImage = $uploadResult['relative_path'];
            }
        }
        
        try {
            $stmt = $conn->prepare("UPDATE projects SET title = ?, description = ?, location = ?, client = ?, start_date = ?, end_date = ?, status = ?, featured_image = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $stmt->execute([$title, $description, $location, $client, $startDate, $endDate, $status, $featuredImage, $projectId]);
            
            $message = 'Project updated successfully!';
            $messageType = 'success';
            
            // Refresh project data
            $stmt = $conn->prepare("SELECT * FROM projects WHERE id = ?");
            $stmt->execute([$projectId]);
            $project = $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            $message = 'Database error: ' . $e->getMessage();
            $messageType = 'error';
        }
    } else {
        $message = 'Please fill in all required fields.';
        $messageType = 'error';
    }
}

include 'includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Edit Project: <?php echo htmlspecialchars($project['title']); ?></h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="projects.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Projects
                    </a>
                    <a href="../project-detail.php?id=<?php echo $project['id']; ?>" class="btn btn-outline-info" target="_blank">
                        <i class="fas fa-eye"></i> View Project
                    </a>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType == 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                    <i class="fas fa-<?php echo $messageType == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Edit Project Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Project Information</h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="title" class="form-label">Project Title *</label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="<?php echo htmlspecialchars($project['title']); ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description *</label>
                                    <textarea class="form-control" id="description" name="description" rows="5" required><?php echo htmlspecialchars($project['description']); ?></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="location" class="form-label">Location *</label>
                                            <input type="text" class="form-control" id="location" name="location" 
                                                   value="<?php echo htmlspecialchars($project['location']); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="client" class="form-label">Client</label>
                                            <input type="text" class="form-control" id="client" name="client" 
                                                   value="<?php echo htmlspecialchars($project['client']); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">Status *</label>
                                            <select class="form-control" id="status" name="status" required>
                                                <option value="">Select Status</option>
                                                <option value="planned" <?php echo $project['status'] == 'planned' ? 'selected' : ''; ?>>Planned</option>
                                                <option value="ongoing" <?php echo $project['status'] == 'ongoing' ? 'selected' : ''; ?>>Ongoing</option>
                                                <option value="completed" <?php echo $project['status'] == 'completed' ? 'selected' : ''; ?>>Completed</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="start_date" class="form-label">Start Date</label>
                                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                                   value="<?php echo $project['start_date']; ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="end_date" class="form-label">End Date</label>
                                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                                   value="<?php echo $project['end_date']; ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Current Featured Image</label>
                                    <?php if ($project['featured_image']): ?>
                                        <div class="current-image mb-3">
                                            <img src="../<?php echo htmlspecialchars($project['featured_image']); ?>" 
                                                 alt="Current featured image" 
                                                 style="max-width: 100%; max-height: 200px; border-radius: 8px;">
                                        </div>
                                    <?php else: ?>
                                        <div class="text-muted mb-3">No image uploaded</div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="featured_image" class="form-label">Update Featured Image</label>
                                    <input type="file" class="form-control" id="featured_image" name="featured_image" 
                                           accept="image/*" onchange="previewImage()">
                                    <div class="form-text">Upload a new featured image (optional)</div>
                                    <div id="imagePreview" class="mt-3" style="display: none;">
                                        <img id="preview" style="max-width: 100%; max-height: 200px; border-radius: 8px;" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="projects.php" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Project
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Project Statistics -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Project Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stat-item text-center">
                                <h4 class="text-primary"><?php echo $project['id']; ?></h4>
                                <small class="text-muted">Project ID</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item text-center">
                                <h4 class="text-info"><?php echo date('M d, Y', strtotime($project['created_at'])); ?></h4>
                                <small class="text-muted">Created</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item text-center">
                                <h4 class="text-warning"><?php echo $project['updated_at'] ? date('M d, Y', strtotime($project['updated_at'])) : 'Never'; ?></h4>
                                <small class="text-muted">Last Updated</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item text-center">
                                <h4 class="text-success">
                                    <?php 
                                    if ($project['start_date'] && $project['end_date']) {
                                        $start = new DateTime($project['start_date']);
                                        $end = new DateTime($project['end_date']);
                                        echo $start->diff($end)->days . ' days';
                                    } else {
                                        echo 'N/A';
                                    }
                                    ?>
                                </h4>
                                <small class="text-muted">Duration</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
function previewImage() {
    const fileInput = document.getElementById('featured_image');
    const imagePreview = document.getElementById('imagePreview');
    const preview = document.getElementById('preview');
    
    if (fileInput.files && fileInput.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            preview.src = e.target.result;
            imagePreview.style.display = 'block';
        };
        
        reader.readAsDataURL(fileInput.files[0]);
    } else {
        imagePreview.style.display = 'none';
    }
}
</script>

<style>
.stat-item {
    padding: 1rem;
    border-radius: 8px;
    background: #f8f9fa;
    margin-bottom: 1rem;
}

.current-image {
    border: 2px dashed #dee2e6;
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
}
</style>

<?php include 'includes/admin_footer.php'; ?>
