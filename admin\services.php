<?php
require_once __DIR__ . '/../includes/functions.php';
requireLogin();

$pageTitle = 'Service Management';

// Handle delete request
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $serviceId = (int)$_GET['delete'];
    $database = new Database();
    $conn = $database->getConnection();
    
    try {
        $stmt = $conn->prepare("DELETE FROM services WHERE id = ?");
        $stmt->execute([$serviceId]);
        $message = 'Service deleted successfully!';
        $messageType = 'success';
    } catch (PDOException $e) {
        $message = 'Error deleting service: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// Handle status toggle
if (isset($_GET['toggle']) && is_numeric($_GET['toggle'])) {
    $serviceId = (int)$_GET['toggle'];
    $database = new Database();
    $conn = $database->getConnection();
    
    try {
        // Get current status
        $stmt = $conn->prepare("SELECT status FROM services WHERE id = ?");
        $stmt->execute([$serviceId]);
        $currentStatus = $stmt->fetchColumn();
        
        // Toggle status
        $newStatus = $currentStatus == 'active' ? 'inactive' : 'active';
        $stmt = $conn->prepare("UPDATE services SET status = ? WHERE id = ?");
        $stmt->execute([$newStatus, $serviceId]);
        
        $message = 'Service status updated successfully!';
        $messageType = 'success';
    } catch (PDOException $e) {
        $message = 'Error updating service: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// Get all services
$allServices = getServices();

include 'includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Service Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="service-add.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Service
                    </a>
                </div>
            </div>

            <?php if (isset($message)): ?>
                <div class="alert alert-<?php echo $messageType == 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                    <i class="fas fa-<?php echo $messageType == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Services Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">All Services</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($allServices)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped data-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Service</th>
                                        <th>Icon</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($allServices as $service): ?>
                                        <tr>
                                            <td><?php echo $service['id']; ?></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($service['image']): ?>
                                                        <img src="../<?php echo htmlspecialchars($service['image']); ?>" 
                                                             alt="<?php echo htmlspecialchars($service['title']); ?>" 
                                                             class="me-3" style="width: 50px; height: 50px; object-fit: cover; border-radius: 8px;">
                                                    <?php endif; ?>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($service['title']); ?></strong>
                                                        <br>
                                                        <small class="text-muted"><?php echo htmlspecialchars(substr($service['description'], 0, 100)) . '...'; ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <i class="<?php echo $service['icon'] ?: 'fas fa-tools'; ?> fa-2x text-primary"></i>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $service['status'] == 'active' ? 'success' : 'secondary'; ?>">
                                                    <?php echo ucfirst($service['status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('M d, Y', strtotime($service['created_at'])); ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="service-edit.php?id=<?php echo $service['id']; ?>" class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="services.php?toggle=<?php echo $service['id']; ?>" class="btn btn-sm btn-outline-warning" title="Toggle Status">
                                                        <i class="fas fa-toggle-<?php echo $service['status'] == 'active' ? 'on' : 'off'; ?>"></i>
                                                    </a>
                                                    <a href="../service-detail.php?id=<?php echo $service['id']; ?>" class="btn btn-sm btn-outline-info" title="View" target="_blank">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteService(<?php echo $service['id']; ?>)" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-tools fa-3x text-muted mb-3"></i>
                            <h4>No Services Found</h4>
                            <p class="text-muted">Start by adding your first service.</p>
                            <a href="service-add.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add New Service
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
function deleteService(id) {
    if (confirm('Are you sure you want to delete this service? This action cannot be undone.')) {
        window.location.href = 'services.php?delete=' + id;
    }
}
</script>

<?php include 'includes/admin_footer.php'; ?>
