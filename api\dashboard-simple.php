<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../includes/functions.php';

$method = $_SERVER['REQUEST_METHOD'];

if ($method !== 'GET') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

// Simple dashboard without authentication for testing
try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Get basic statistics
    $stats = [
        'projects' => [
            'total' => 0,
            'completed' => 0,
            'ongoing' => 0,
            'planned' => 0,
            'recent' => 0
        ],
        'services' => [
            'total' => 0,
            'active' => 0
        ],
        'messages' => [
            'total' => 0,
            'new' => 0,
            'read' => 0,
            'recent' => 0
        ],
        'media' => [
            'total' => 0,
            'images' => 0,
            'videos' => 0
        ],
        'recent_projects' => [],
        'recent_messages' => [],
        'last_updated' => date('Y-m-d H:i:s')
    ];
    
    // Try to get projects statistics
    try {
        $stmt = $conn->prepare("SELECT 
            COUNT(*) as total_projects,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_projects,
            SUM(CASE WHEN status = 'ongoing' THEN 1 ELSE 0 END) as ongoing_projects,
            SUM(CASE WHEN status = 'planned' THEN 1 ELSE 0 END) as planned_projects
            FROM projects");
        $stmt->execute();
        $projectStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stats['projects'] = [
            'total' => (int)$projectStats['total_projects'],
            'completed' => (int)$projectStats['completed_projects'],
            'ongoing' => (int)$projectStats['ongoing_projects'],
            'planned' => (int)$projectStats['planned_projects'],
            'recent' => 0
        ];
    } catch (Exception $e) {
        error_log("Error getting project stats: " . $e->getMessage());
    }
    
    // Try to get services statistics
    try {
        $stmt = $conn->prepare("SELECT 
            COUNT(*) as total_services,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_services
            FROM services");
        $stmt->execute();
        $serviceStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stats['services'] = [
            'total' => (int)$serviceStats['total_services'],
            'active' => (int)$serviceStats['active_services']
        ];
    } catch (Exception $e) {
        error_log("Error getting service stats: " . $e->getMessage());
    }
    
    // Try to get messages statistics
    try {
        $stmt = $conn->prepare("SELECT 
            COUNT(*) as total_messages,
            SUM(CASE WHEN status = 'new' THEN 1 ELSE 0 END) as new_messages,
            SUM(CASE WHEN status = 'read' THEN 1 ELSE 0 END) as read_messages
            FROM messages");
        $stmt->execute();
        $messageStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stats['messages'] = [
            'total' => (int)$messageStats['total_messages'],
            'new' => (int)$messageStats['new_messages'],
            'read' => (int)$messageStats['read_messages'],
            'recent' => 0
        ];
    } catch (Exception $e) {
        error_log("Error getting message stats: " . $e->getMessage());
    }
    
    // Try to get media statistics
    try {
        $stmt = $conn->prepare("SELECT 
            COUNT(*) as total_media,
            SUM(CASE WHEN file_type = 'image' THEN 1 ELSE 0 END) as total_images,
            SUM(CASE WHEN file_type = 'video' THEN 1 ELSE 0 END) as total_videos
            FROM media");
        $stmt->execute();
        $mediaStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stats['media'] = [
            'total' => (int)$mediaStats['total_media'],
            'images' => (int)$mediaStats['total_images'],
            'videos' => (int)$mediaStats['total_videos']
        ];
    } catch (Exception $e) {
        error_log("Error getting media stats: " . $e->getMessage());
    }
    
    jsonResponse([
        'success' => true,
        'data' => $stats
    ]);
    
} catch (Exception $e) {
    error_log("Dashboard simple error: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Error fetching dashboard data: ' . $e->getMessage()], 500);
}
?>
