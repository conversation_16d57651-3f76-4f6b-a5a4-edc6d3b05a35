<?php
require_once __DIR__ . '/../config/database.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Authentication functions
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function requireLogin() {
    if (!isLoggedIn()) {
        // Check if we're already in admin directory
        $currentPath = $_SERVER['REQUEST_URI'];
        if (strpos($currentPath, '/admin/') !== false) {
            header('Location: login.php');
        } else {
            header('Location: admin/login.php');
        }
        exit();
    }
}

function login($username, $password) {
    $database = new Database();
    $conn = $database->getConnection();

    $stmt = $conn->prepare("SELECT id, username, email, password, role, status FROM users WHERE username = ? OR email = ?");
    $stmt->execute([$username, $username]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user && password_verify($password, $user['password'])) {
        // Check if user account is active
        if (isset($user['status']) && $user['status'] === 'inactive') {
            return false; // Account is inactive
        }

        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];

        // Update last login time
        try {
            $updateStmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
            $updateStmt->execute([$user['id']]);
        } catch (PDOException $e) {
            // Log error but don't fail login
            error_log("Failed to update last login time: " . $e->getMessage());
        }

        return true;
    }
    return false;
}

function logout() {
    session_destroy();
    header('Location: login.php');
    exit();
}

// Utility functions
function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

function uploadFile($file, $uploadDir = 'uploads/') {
    // Validate file upload
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        $errorMessages = [
            UPLOAD_ERR_INI_SIZE => 'File too large (exceeds server limit)',
            UPLOAD_ERR_FORM_SIZE => 'File too large (exceeds form limit)',
            UPLOAD_ERR_PARTIAL => 'File upload incomplete',
            UPLOAD_ERR_NO_FILE => 'No file uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
        ];

        $errorCode = $file['error'] ?? UPLOAD_ERR_NO_FILE;
        return ['success' => false, 'message' => $errorMessages[$errorCode] ?? 'Unknown upload error'];
    }

    // Create upload directory if it doesn't exist
    if (!file_exists($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            return ['success' => false, 'message' => 'Failed to create upload directory'];
        }
    }

    // Validate file type by extension and MIME type
    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'mov', 'avi', 'wmv'];
    $allowedMimeTypes = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
        'video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/x-ms-wmv'
    ];

    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $fileMimeType = $file['type'];

    if (!in_array($fileExtension, $allowedExtensions)) {
        return ['success' => false, 'message' => 'Invalid file extension. Allowed: ' . implode(', ', $allowedExtensions)];
    }

    if (!in_array($fileMimeType, $allowedMimeTypes)) {
        return ['success' => false, 'message' => 'Invalid file type'];
    }

    // Check file size (max 50MB)
    $maxFileSize = 50 * 1024 * 1024; // 50MB
    if ($file['size'] > $maxFileSize) {
        return ['success' => false, 'message' => 'File too large. Maximum size: 50MB'];
    }

    // Generate secure filename
    $fileName = uniqid('media_', true) . '_' . time() . '.' . $fileExtension;
    $filePath = $uploadDir . $fileName;

    // Additional security: verify file is actually an image/video
    if (strpos($fileMimeType, 'image/') === 0) {
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            return ['success' => false, 'message' => 'Invalid image file'];
        }
    }

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        // Set proper file permissions
        chmod($filePath, 0644);

        return [
            'success' => true,
            'filename' => $fileName,
            'path' => $filePath,
            'relative_path' => str_replace('../', '', $uploadDir) . $fileName,
            'size' => $file['size'],
            'type' => $fileMimeType
        ];
    }

    return ['success' => false, 'message' => 'Failed to move uploaded file'];
}

// Database helper functions
function getServices($status = 'active') {
    $database = new Database();
    $conn = $database->getConnection();
    
    $sql = "SELECT * FROM services";
    if ($status) {
        $sql .= " WHERE status = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$status]);
    } else {
        $stmt = $conn->prepare($sql);
        $stmt->execute();
    }
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getProjects($status = null, $limit = null) {
    $database = new Database();
    $conn = $database->getConnection();

    $sql = "SELECT * FROM projects";
    $params = [];

    if ($status) {
        $sql .= " WHERE status = ?";
        $params[] = $status;
    }

    $sql .= " ORDER BY created_at DESC";

    if ($limit) {
        $sql .= " LIMIT " . (int)$limit;
    }

    $stmt = $conn->prepare($sql);
    $stmt->execute($params);

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getProjectById($id) {
    $database = new Database();
    $conn = $database->getConnection();

    $stmt = $conn->prepare("SELECT * FROM projects WHERE id = ?");
    $stmt->execute([$id]);
    $project = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($project && $project['gallery']) {
        $project['gallery'] = json_decode($project['gallery'], true);
    }

    return $project;
}

function getServiceById($id) {
    $database = new Database();
    $conn = $database->getConnection();

    $stmt = $conn->prepare("SELECT * FROM services WHERE id = ? AND status = 'active'");
    $stmt->execute([$id]);

    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function getRelatedProjects($currentProjectId, $limit = 3) {
    $database = new Database();
    $conn = $database->getConnection();

    $limit = (int)$limit; // Ensure it's an integer
    $stmt = $conn->prepare("SELECT * FROM projects WHERE id != ? ORDER BY created_at DESC LIMIT " . $limit);
    $stmt->execute([$currentProjectId]);

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getRelatedServices($currentServiceId, $limit = 3) {
    $database = new Database();
    $conn = $database->getConnection();

    $limit = (int)$limit; // Ensure it's an integer
    $stmt = $conn->prepare("SELECT * FROM services WHERE id != ? AND status = 'active' ORDER BY created_at DESC LIMIT " . $limit);
    $stmt->execute([$currentServiceId]);

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getMedia($type = null, $limit = null) {
    $database = new Database();
    $conn = $database->getConnection();

    $sql = "SELECT * FROM media";
    $params = [];

    if ($type) {
        $sql .= " WHERE file_type = ?";
        $params[] = $type;
    }

    $sql .= " ORDER BY uploaded_at DESC";

    if ($limit) {
        $sql .= " LIMIT " . (int)$limit;
    }

    $stmt = $conn->prepare($sql);
    $stmt->execute($params);

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getMediaById($id) {
    $database = new Database();
    $conn = $database->getConnection();

    $stmt = $conn->prepare("SELECT * FROM media WHERE id = ?");
    $stmt->execute([$id]);

    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function getUserById($id) {
    $database = new Database();
    $conn = $database->getConnection();

    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$id]);

    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function getMessages($status = null) {
    $database = new Database();
    $conn = $database->getConnection();

    $sql = "SELECT * FROM messages";
    $params = [];

    if ($status) {
        $sql .= " WHERE status = ?";
        $params[] = $status;
    }

    $sql .= " ORDER BY created_at DESC";

    $stmt = $conn->prepare($sql);
    $stmt->execute($params);

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getSetting($key) {
    $database = new Database();
    $conn = $database->getConnection();
    
    $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
    $stmt->execute([$key]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    return $result ? $result['setting_value'] : null;
}

function updateSetting($key, $value) {
    $database = new Database();
    $conn = $database->getConnection();
    
    $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
    return $stmt->execute([$key, $value, $value]);
}

// API response helper
function jsonResponse($data, $status = 200) {
    http_response_code($status);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit();
}

// Pagination helper
function paginate($table, $page = 1, $perPage = 10, $conditions = '') {
    $database = new Database();
    $conn = $database->getConnection();

    $offset = ($page - 1) * $perPage;

    // Get total count
    $countSql = "SELECT COUNT(*) as total FROM $table $conditions";
    $stmt = $conn->prepare($countSql);
    $stmt->execute();
    $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Get data
    $dataSql = "SELECT * FROM $table $conditions LIMIT " . (int)$perPage . " OFFSET " . (int)$offset;
    $stmt = $conn->prepare($dataSql);
    $stmt->execute();
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return [
        'data' => $data,
        'total' => $total,
        'page' => $page,
        'perPage' => $perPage,
        'totalPages' => ceil($total / $perPage)
    ];
}

// Generate breadcrumbs
function generateBreadcrumbs($items) {
    $breadcrumbs = '<nav aria-label="breadcrumb"><ol class="breadcrumb">';
    foreach ($items as $item) {
        if (isset($item['url'])) {
            $breadcrumbs .= '<li class="breadcrumb-item"><a href="' . $item['url'] . '">' . $item['title'] . '</a></li>';
        } else {
            $breadcrumbs .= '<li class="breadcrumb-item active">' . $item['title'] . '</li>';
        }
    }
    $breadcrumbs .= '</ol></nav>';
    return $breadcrumbs;
}
?>
