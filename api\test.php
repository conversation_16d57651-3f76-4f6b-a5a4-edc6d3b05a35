<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../includes/functions.php';

// Simple test endpoint without authentication
$method = $_SERVER['REQUEST_METHOD'];

if ($method !== 'GET') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

// Test basic functionality
try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Test database connection
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM projects");
    $stmt->execute();
    $projectCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    jsonResponse([
        'success' => true,
        'message' => 'Test endpoint working',
        'data' => [
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $method,
            'project_count' => (int)$projectCount,
            'headers' => getallheaders()
        ]
    ]);
    
} catch (Exception $e) {
    jsonResponse([
        'success' => false, 
        'message' => 'Test failed: ' . $e->getMessage(),
        'error_details' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ], 500);
}
?>
