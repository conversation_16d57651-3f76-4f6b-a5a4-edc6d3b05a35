<?php
require_once __DIR__ . '/../includes/functions.php';
requireLogin();

$pageTitle = 'Upload Media';

$message = '';
$messageType = '';

// Handle file upload
if ($_POST && isset($_FILES['files'])) {
    $uploadedFiles = [];
    $errors = [];
    
    // Handle multiple file uploads
    $files = $_FILES['files'];
    $fileCount = count($files['name']);
    
    for ($i = 0; $i < $fileCount; $i++) {
        // Skip empty files
        if ($files['error'][$i] === UPLOAD_ERR_NO_FILE) {
            continue;
        }
        
        // Create individual file array for processing
        $file = [
            'name' => $files['name'][$i],
            'type' => $files['type'][$i],
            'tmp_name' => $files['tmp_name'][$i],
            'error' => $files['error'][$i],
            'size' => $files['size'][$i]
        ];
        
        // Get metadata for this file
        $title = sanitizeInput($_POST['titles'][$i] ?? '');
        $altText = sanitizeInput($_POST['alt_texts'][$i] ?? '');
        $category = sanitizeInput($_POST['categories'][$i] ?? '');
        
        if (empty($title)) {
            $title = pathinfo($file['name'], PATHINFO_FILENAME);
            $title = str_replace(['_', '-'], ' ', $title);
            $title = ucwords($title);
        }
        
        $uploadResult = uploadFile($file, '../uploads/');
        
        if ($uploadResult['success']) {
            $database = new Database();
            $conn = $database->getConnection();
            
            // Determine file type from MIME type
            $fileType = strpos($uploadResult['type'], 'image/') === 0 ? 'image' : 'video';
            
            try {
                $stmt = $conn->prepare("INSERT INTO media (title, filename, file_path, file_type, file_size, alt_text, category) VALUES (?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    $title,
                    $uploadResult['filename'],
                    $uploadResult['relative_path'],
                    $fileType,
                    $uploadResult['size'],
                    $altText,
                    $category
                ]);
                
                $uploadedFiles[] = [
                    'title' => $title,
                    'filename' => $uploadResult['filename'],
                    'type' => $fileType
                ];
                
            } catch (PDOException $e) {
                $errors[] = "Database error for {$file['name']}: " . $e->getMessage();
            }
        } else {
            $errors[] = "Upload failed for {$file['name']}: " . $uploadResult['message'];
        }
    }
    
    // Set success/error messages
    if (!empty($uploadedFiles)) {
        $count = count($uploadedFiles);
        $message = $count . ' file' . ($count > 1 ? 's' : '') . ' uploaded successfully!';
        $messageType = 'success';
    }
    
    if (!empty($errors)) {
        if (empty($uploadedFiles)) {
            $message = 'Upload failed: ' . implode(', ', $errors);
            $messageType = 'error';
        } else {
            $message .= ' Some files had errors: ' . implode(', ', $errors);
            $messageType = 'warning';
        }
    }
}

// Get media categories for dropdown
$categories = [
    'projects' => 'Projects',
    'groundworks' => 'Groundworks', 
    'rc-frames' => 'RC Frames',
    'basements' => 'Basements',
    'landscaping' => 'Landscaping',
    'team' => 'Team',
    'equipment' => 'Equipment',
    'before-after' => 'Before & After',
    'progress' => 'Progress Photos',
    'certificates' => 'Certificates'
];

include 'includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-upload"></i> Upload Media
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="media.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-images"></i> View All Media
                        </a>
                    </div>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType == 'error' ? 'danger' : ($messageType == 'warning' ? 'warning' : 'success'); ?> alert-dismissible fade show">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Upload Form -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-cloud-upload-alt"></i> Upload Files
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" enctype="multipart/form-data" id="uploadForm">
                                <!-- Drag & Drop Area -->
                                <div class="upload-area" id="uploadArea">
                                    <div class="upload-content">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <h4>Drag & Drop Files Here</h4>
                                        <p class="text-muted">or click to browse files</p>
                                        <input type="file" id="fileInput" name="files[]" multiple 
                                               accept="image/jpeg,image/jpg,image/png,image/gif,image/webp,video/mp4,video/quicktime,video/x-msvideo"
                                               style="display: none;">
                                        <button type="button" class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                                            <i class="fas fa-folder-open"></i> Browse Files
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- File Preview Area -->
                                <div id="filePreviewArea" style="display: none;">
                                    <h6 class="mt-4 mb-3">Selected Files:</h6>
                                    <div id="fileList"></div>
                                </div>
                                
                                <!-- Submit Button -->
                                <div class="text-center mt-4" id="submitArea" style="display: none;">
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="fas fa-upload"></i> Upload Files
                                    </button>
                                    <button type="button" class="btn btn-secondary btn-lg ms-2" onclick="clearFiles()">
                                        <i class="fas fa-times"></i> Clear All
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Upload Guidelines -->
                <div class="col-lg-4">
                    <div class="card shadow">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle"></i> Upload Guidelines
                            </h5>
                        </div>
                        <div class="card-body">
                            <h6>Supported Formats:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-image text-primary"></i> Images: JPG, PNG, GIF, WebP</li>
                                <li><i class="fas fa-video text-danger"></i> Videos: MP4, MOV, AVI</li>
                            </ul>
                            
                            <h6 class="mt-3">File Size Limits:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-image text-primary"></i> Images: Max 10MB</li>
                                <li><i class="fas fa-video text-danger"></i> Videos: Max 50MB</li>
                            </ul>
                            
                            <h6 class="mt-3">Best Practices:</h6>
                            <ul class="small">
                                <li>Use descriptive filenames</li>
                                <li>Add meaningful titles and descriptions</li>
                                <li>Categorize files appropriately</li>
                                <li>Compress large images before upload</li>
                                <li>Use high-quality images for projects</li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- Recent Uploads -->
                    <div class="card shadow mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clock"></i> Recent Uploads
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php
                            // Get recent uploads
                            $database = new Database();
                            $conn = $database->getConnection();
                            $stmt = $conn->prepare("SELECT title, file_type, uploaded_at FROM media ORDER BY uploaded_at DESC LIMIT 5");
                            $stmt->execute();
                            $recentUploads = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            ?>
                            
                            <?php if (!empty($recentUploads)): ?>
                                <?php foreach ($recentUploads as $upload): ?>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-<?php echo $upload['file_type'] == 'image' ? 'image' : 'video'; ?> text-muted me-2"></i>
                                        <div class="flex-grow-1">
                                            <div class="small fw-bold"><?php echo htmlspecialchars($upload['title']); ?></div>
                                            <div class="text-muted small"><?php echo date('M d, H:i', strtotime($upload['uploaded_at'])); ?></div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-muted small">No recent uploads</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<style>
.upload-area {
    border: 3px dashed #dee2e6;
    border-radius: 10px;
    padding: 60px 20px;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: var(--primary-color);
    background-color: #fff5f2;
}

.upload-area.dragover {
    border-color: #28a745;
    background-color: #f0fff4;
}

.upload-content h4 {
    color: #6c757d;
    margin-bottom: 10px;
}

.file-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: #fff;
    transition: box-shadow 0.3s ease;
}

.file-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.file-preview {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.file-info {
    flex-grow: 1;
}

.file-size {
    font-size: 0.875rem;
    color: #6c757d;
}

.file-type-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.progress-bar-container {
    height: 4px;
    background-color: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 8px;
}

.progress-bar {
    height: 100%;
    background-color: #28a745;
    width: 0%;
    transition: width 0.3s ease;
}

.remove-file {
    color: #dc3545;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.remove-file:hover {
    background-color: #f8d7da;
}

.form-control-sm {
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    .upload-area {
        padding: 40px 15px;
    }

    .file-item {
        padding: 10px;
    }

    .file-preview,
    .file-type-icon {
        width: 60px;
        height: 60px;
    }
}
</style>

<script>
let selectedFiles = [];
const maxFileSize = 50 * 1024 * 1024; // 50MB
const maxImageSize = 10 * 1024 * 1024; // 10MB

// Drag and drop functionality
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');

uploadArea.addEventListener('click', () => {
    fileInput.click();
});

uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
});

uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');

    const files = Array.from(e.dataTransfer.files);
    handleFiles(files);
});

fileInput.addEventListener('change', (e) => {
    const files = Array.from(e.target.files);
    handleFiles(files);
});

function handleFiles(files) {
    files.forEach(file => {
        if (validateFile(file)) {
            selectedFiles.push({
                file: file,
                title: generateTitle(file.name),
                altText: '',
                category: ''
            });
        }
    });

    updateFileList();
    updateSubmitArea();
}

function validateFile(file) {
    // Check file type
    const allowedTypes = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
        'video/mp4', 'video/quicktime', 'video/x-msvideo'
    ];

    if (!allowedTypes.includes(file.type)) {
        alert(`File type not supported: ${file.name}`);
        return false;
    }

    // Check file size
    const isImage = file.type.startsWith('image/');
    const sizeLimit = isImage ? maxImageSize : maxFileSize;

    if (file.size > sizeLimit) {
        const limitMB = Math.round(sizeLimit / (1024 * 1024));
        alert(`File too large: ${file.name}. Maximum size is ${limitMB}MB for ${isImage ? 'images' : 'videos'}.`);
        return false;
    }

    return true;
}

function generateTitle(filename) {
    const nameWithoutExt = filename.replace(/\.[^/.]+$/, "");
    return nameWithoutExt.replace(/[_-]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function updateFileList() {
    const fileList = document.getElementById('fileList');
    const filePreviewArea = document.getElementById('filePreviewArea');

    if (selectedFiles.length === 0) {
        filePreviewArea.style.display = 'none';
        return;
    }

    filePreviewArea.style.display = 'block';
    fileList.innerHTML = '';

    selectedFiles.forEach((fileData, index) => {
        const fileItem = createFileItem(fileData, index);
        fileList.appendChild(fileItem);
    });
}

function createFileItem(fileData, index) {
    const file = fileData.file;
    const isImage = file.type.startsWith('image/');

    const fileItem = document.createElement('div');
    fileItem.className = 'file-item';

    fileItem.innerHTML = `
        <div class="row align-items-center">
            <div class="col-auto">
                ${isImage ?
                    `<img class="file-preview" id="preview-${index}" alt="Preview">` :
                    `<div class="file-type-icon">
                        <i class="fas fa-video fa-2x text-muted"></i>
                    </div>`
                }
            </div>
            <div class="col file-info">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label small">Title</label>
                        <input type="text" class="form-control form-control-sm"
                               name="titles[]" value="${fileData.title}"
                               onchange="updateFileData(${index}, 'title', this.value)">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label small">Category</label>
                        <select class="form-control form-control-sm" name="categories[]"
                                onchange="updateFileData(${index}, 'category', this.value)">
                            <option value="">Select Category</option>
                            <?php foreach ($categories as $key => $label): ?>
                            <option value="<?php echo $key; ?>"><?php echo $label; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col">
                        <label class="form-label small">Description</label>
                        <textarea class="form-control form-control-sm" rows="2"
                                  name="alt_texts[]" placeholder="Enter description..."
                                  onchange="updateFileData(${index}, 'altText', this.value)">${fileData.altText}</textarea>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="file-size text-muted">
                        ${file.name} (${formatFileSize(file.size)})
                    </small>
                </div>
            </div>
            <div class="col-auto">
                <i class="fas fa-times remove-file" onclick="removeFile(${index})"
                   title="Remove file"></i>
            </div>
        </div>
    `;

    // Load image preview
    if (isImage) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = fileItem.querySelector(`#preview-${index}`);
            if (preview) {
                preview.src = e.target.result;
            }
        };
        reader.readAsDataURL(file);
    }

    return fileItem;
}

function updateFileData(index, field, value) {
    if (selectedFiles[index]) {
        selectedFiles[index][field] = value;
    }
}

function removeFile(index) {
    selectedFiles.splice(index, 1);
    updateFileList();
    updateSubmitArea();
}

function clearFiles() {
    selectedFiles = [];
    fileInput.value = '';
    updateFileList();
    updateSubmitArea();
}

function updateSubmitArea() {
    const submitArea = document.getElementById('submitArea');
    submitArea.style.display = selectedFiles.length > 0 ? 'block' : 'none';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Form submission handling
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    if (selectedFiles.length === 0) {
        e.preventDefault();
        alert('Please select at least one file to upload.');
        return;
    }

    // Create a new FormData object with selected files
    const formData = new FormData();

    selectedFiles.forEach((fileData, index) => {
        formData.append('files[]', fileData.file);
        formData.append('titles[]', fileData.title);
        formData.append('alt_texts[]', fileData.altText);
        formData.append('categories[]', fileData.category);
    });

    // Replace form data
    const form = this;
    const originalFormData = new FormData(form);

    // Clear existing form data and append new data
    for (let [key, value] of formData.entries()) {
        form.querySelector(`input[name="${key}"]`) ||
        form.querySelector(`textarea[name="${key}"]`) ||
        form.querySelector(`select[name="${key}"]`) ||
        (() => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = value;
            form.appendChild(input);
        })();
    }

    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
    submitBtn.disabled = true;

    // Re-enable button after timeout (in case of errors)
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 30000);
});
</script>

<?php include 'includes/admin_footer.php'; ?>
