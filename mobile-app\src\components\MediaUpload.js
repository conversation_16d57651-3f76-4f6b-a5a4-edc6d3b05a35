import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Image,
  Modal,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { Button, ProgressBar } from 'react-native-paper';
import { theme } from '../theme/theme';

const { width } = Dimensions.get('window');

export default function MediaUpload({
  onImageSelected,
  onUploadComplete,
  initialImage,
  allowMultiple = false,
  maxImages = 5,
  compressionQuality = 0.8,
  aspectRatio = [16, 9],
  style,
}) {
  const [selectedImages, setSelectedImages] = useState(initialImage ? [initialImage] : []);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Please grant camera roll permissions to upload images.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Settings', onPress: () => Linking.openSettings() },
        ]
      );
      return false;
    }
    return true;
  };

  const pickImageFromLibrary = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: aspectRatio,
      quality: compressionQuality,
      allowsMultipleSelection: allowMultiple,
    });

    if (!result.canceled) {
      handleImageSelection(result.assets);
    }
  };

  const takePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Please grant camera permissions to take photos.');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: aspectRatio,
      quality: compressionQuality,
    });

    if (!result.canceled) {
      handleImageSelection(result.assets);
    }
  };

  const handleImageSelection = (assets) => {
    if (allowMultiple) {
      const newImages = [...selectedImages, ...assets].slice(0, maxImages);
      setSelectedImages(newImages);
      onImageSelected && onImageSelected(newImages);
    } else {
      setSelectedImages(assets);
      onImageSelected && onImageSelected(assets[0]);
    }
  };

  const removeImage = (index) => {
    const newImages = selectedImages.filter((_, i) => i !== index);
    setSelectedImages(newImages);
    onImageSelected && onImageSelected(allowMultiple ? newImages : newImages[0]);
  };

  const simulateUpload = async () => {
    setUploading(true);
    setUploadProgress(0);

    // Simulate upload progress
    for (let i = 0; i <= 100; i += 10) {
      setUploadProgress(i / 100);
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    setUploading(false);
    onUploadComplete && onUploadComplete(selectedImages);
    Alert.alert('Success', 'Images uploaded successfully!');
  };

  const showImageOptions = () => {
    Alert.alert(
      'Select Image',
      'Choose how you want to add an image',
      [
        { text: 'Camera', onPress: takePhoto },
        { text: 'Photo Library', onPress: pickImageFromLibrary },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const openImageModal = (index) => {
    setSelectedImageIndex(index);
    setShowImageModal(true);
  };

  const ImagePreview = ({ image, index, onRemove, onPress }) => (
    <View style={styles.imagePreview}>
      <TouchableOpacity onPress={() => onPress(index)}>
        <Image source={{ uri: image.uri }} style={styles.previewImage} />
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => onRemove(index)}
      >
        <Ionicons name="close-circle" size={24} color={theme.colors.error} />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={[styles.container, style]}>
      {/* Upload Button */}
      <TouchableOpacity
        style={styles.uploadButton}
        onPress={showImageOptions}
        disabled={uploading || (allowMultiple && selectedImages.length >= maxImages)}
      >
        <Ionicons 
          name="camera" 
          size={32} 
          color={uploading ? theme.colors.disabled : theme.colors.primary} 
        />
        <Text style={[styles.uploadText, uploading && styles.uploadTextDisabled]}>
          {selectedImages.length === 0 
            ? 'Add Images' 
            : allowMultiple 
              ? `Add More (${selectedImages.length}/${maxImages})`
              : 'Change Image'
          }
        </Text>
      </TouchableOpacity>

      {/* Upload Progress */}
      {uploading && (
        <View style={styles.progressContainer}>
          <Text style={styles.progressText}>Uploading... {Math.round(uploadProgress * 100)}%</Text>
          <ProgressBar 
            progress={uploadProgress} 
            color={theme.colors.primary}
            style={styles.progressBar}
          />
        </View>
      )}

      {/* Image Previews */}
      {selectedImages.length > 0 && (
        <View style={styles.previewContainer}>
          <Text style={styles.previewTitle}>Selected Images:</Text>
          <View style={styles.imageGrid}>
            {selectedImages.map((image, index) => (
              <ImagePreview
                key={index}
                image={image}
                index={index}
                onRemove={removeImage}
                onPress={openImageModal}
              />
            ))}
          </View>
          
          {/* Upload Button */}
          <Button
            mode="contained"
            onPress={simulateUpload}
            disabled={uploading}
            style={styles.uploadActionButton}
            icon="cloud-upload"
          >
            {uploading ? 'Uploading...' : 'Upload Images'}
          </Button>
        </View>
      )}

      {/* Image Modal */}
      <Modal
        visible={showImageModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowImageModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowImageModal(false)}
            >
              <Ionicons name="close" size={30} color="white" />
            </TouchableOpacity>
            
            {selectedImages[selectedImageIndex] && (
              <Image
                source={{ uri: selectedImages[selectedImageIndex].uri }}
                style={styles.modalImage}
                resizeMode="contain"
              />
            )}
            
            {selectedImages.length > 1 && (
              <View style={styles.modalNavigation}>
                <TouchableOpacity
                  onPress={() => setSelectedImageIndex(Math.max(0, selectedImageIndex - 1))}
                  disabled={selectedImageIndex === 0}
                >
                  <Ionicons 
                    name="chevron-back" 
                    size={30} 
                    color={selectedImageIndex === 0 ? '#666' : 'white'} 
                  />
                </TouchableOpacity>
                
                <Text style={styles.modalCounter}>
                  {selectedImageIndex + 1} / {selectedImages.length}
                </Text>
                
                <TouchableOpacity
                  onPress={() => setSelectedImageIndex(Math.min(selectedImages.length - 1, selectedImageIndex + 1))}
                  disabled={selectedImageIndex === selectedImages.length - 1}
                >
                  <Ionicons 
                    name="chevron-forward" 
                    size={30} 
                    color={selectedImageIndex === selectedImages.length - 1 ? '#666' : 'white'} 
                  />
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: theme.spacing.md,
  },
  uploadButton: {
    borderWidth: 2,
    borderColor: theme.colors.primary,
    borderStyle: 'dashed',
    borderRadius: theme.roundness,
    padding: theme.spacing.xl,
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  uploadText: {
    marginTop: theme.spacing.sm,
    fontSize: 16,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  uploadTextDisabled: {
    color: theme.colors.disabled,
  },
  progressContainer: {
    marginTop: theme.spacing.md,
    padding: theme.spacing.md,
    backgroundColor: 'white',
    borderRadius: theme.roundness,
    ...theme.shadows.small,
  },
  progressText: {
    fontSize: 14,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
  },
  previewContainer: {
    marginTop: theme.spacing.md,
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: theme.spacing.md,
  },
  imagePreview: {
    position: 'relative',
    margin: theme.spacing.xs,
  },
  previewImage: {
    width: (width - 80) / 3,
    height: (width - 80) / 3,
    borderRadius: theme.roundness,
  },
  removeButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: 'white',
    borderRadius: 12,
  },
  uploadActionButton: {
    marginTop: theme.spacing.sm,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalCloseButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 1,
  },
  modalImage: {
    width: '90%',
    height: '70%',
  },
  modalNavigation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '80%',
    marginTop: 20,
  },
  modalCounter: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
});
