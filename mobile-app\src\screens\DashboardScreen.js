import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Dimensions,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  ActivityIndicator,
  Chip,
  Avatar,
  List,
} from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import * as SecureStore from 'expo-secure-store';
import { useAuth } from '../context/AuthContext';
import { apiService } from '../services/apiService';
import { SimpleLoader } from '../components/LoadingScreen';
import { theme } from '../theme/theme';

const { width } = Dimensions.get('window');

export default function DashboardScreen({ navigation }) {
  const [stats, setStats] = useState({});
  const [recentProjects, setRecentProjects] = useState([]);
  const [recentMessages, setRecentMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const { user, logout } = useAuth();

  useFocusEffect(
    useCallback(() => {
      loadDashboardData();
    }, [])
  );

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      console.log('🔄 Loading dashboard data...');

      // Check if we have a token and ensure it's set in the API service
      if (user && !apiService.getAuthStatus().hasToken) {
        console.log('⚠️ User is logged in but API service has no token, attempting to restore...');
        // Try to get token from secure storage and set it
        try {
          const storedToken = await SecureStore.getItemAsync('authToken');
          if (storedToken) {
            console.log('🔑 Restoring token from secure storage');
            apiService.setAuthToken(storedToken);
          }
        } catch (error) {
          console.error('Failed to restore token:', error);
        }
      }

      // Load dashboard statistics with caching
      const dashboardResponse = await apiService.getDashboardStatsWithCache();
      console.log('📊 Dashboard response:', dashboardResponse);

      if (dashboardResponse.fromCache) {
        console.log('📦 Dashboard data loaded from cache');
      }

      if (dashboardResponse.success) {
        const data = dashboardResponse.data;

        // Set statistics
        setStats({
          totalProjects: data.projects.total,
          completedProjects: data.projects.completed,
          ongoingProjects: data.projects.ongoing,
          plannedProjects: data.projects.planned,
          newMessages: data.messages.new,
          totalMessages: data.messages.total,
          totalServices: data.services.total,
          totalMedia: data.media.total,
        });

        // Set recent data
        setRecentProjects(data.recent_projects || []);
        setRecentMessages(data.recent_messages || []);

        console.log('✅ Dashboard data loaded successfully');
      }
    } catch (error) {
      console.error('❌ Error loading dashboard data:', error);
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });

      // Fallback to individual API calls with caching if dashboard endpoint fails
      try {
        console.log('🔄 Trying fallback API calls with cache...');
        const [projectsResponse, messagesResponse] = await Promise.all([
          apiService.getProjectsWithCache({ limit: 5 }),
          apiService.getMessagesWithCache({ status: 'new', limit: 5 }),
        ]);

        if (projectsResponse.success) {
          setRecentProjects(projectsResponse.data.projects);
          const projects = projectsResponse.data.projects;
          const completedCount = projects.filter(p => p.status === 'completed').length;
          const ongoingCount = projects.filter(p => p.status === 'ongoing').length;

          setStats({
            totalProjects: projectsResponse.data.pagination.total,
            completedProjects: completedCount,
            ongoingProjects: ongoingCount,
            newMessages: messagesResponse.success ? messagesResponse.data.pagination.total : 0,
          });

          console.log('✅ Fallback data loaded successfully');
        }

        if (messagesResponse.success) {
          setRecentMessages(messagesResponse.data.messages);
        }
      } catch (fallbackError) {
        console.error('❌ Fallback dashboard loading also failed:', fallbackError);
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const testConnection = async () => {
    try {
      console.log('🧪 Testing API connection...');

      // Check auth status first
      const authStatus = apiService.getAuthStatus();
      console.log('🔑 Auth status:', authStatus);

      // Test basic connection first
      try {
        const testResponse = await apiService.testConnection();
        console.log('✅ Basic test response:', testResponse);
      } catch (testError) {
        console.log('❌ Basic test failed:', testError.message);
      }

      // Test debug endpoint
      try {
        const debugResponse = await apiService.debugConnection();
        console.log('🔍 Debug response:', debugResponse);

        if (debugResponse.success) {
          const data = debugResponse.data;
          alert(`Debug Info:
- Database: ${data.database_connection}
- Auth Token in Request: ${data.auth_token_found ? 'Found' : 'Missing'}
- Auth Token in App: ${authStatus.hasToken ? 'Found' : 'Missing'}
- Token Verification: ${data.token_verification || 'Not tested'}
- User Count: ${data.user_count || 'Unknown'}
- User Info: ${user ? user.username : 'Not logged in'}

Check console for full details.`);
        }
      } catch (debugError) {
        console.log('❌ Debug test failed:', debugError.message);
        alert('Debug test failed: ' + debugError.message);
      }

    } catch (error) {
      console.error('❌ Connection test failed:', error);
      alert('Connection test failed: ' + error.message);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadDashboardData();
  };

  const handleLogout = async () => {
    await logout();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return theme.colors.success;
      case 'ongoing':
        return theme.colors.warning;
      case 'planned':
        return theme.colors.info;
      default:
        return theme.colors.placeholder;
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <SimpleLoader size={32} />
        <Text style={styles.loadingText}>Loading dashboard...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View>
            <Title style={styles.welcomeTitle}>Welcome back,</Title>
            <Paragraph style={styles.userName}>{user?.username}</Paragraph>
          </View>
          <Button
            mode="outlined"
            onPress={handleLogout}
            icon="logout"
            compact
            style={styles.logoutButton}
          >
            Logout
          </Button>
        </View>
      </View>

      {/* Statistics Cards */}
      <View style={styles.statsContainer}>
        <View style={styles.statsRow}>
          <Card style={[styles.statCard, { backgroundColor: theme.colors.primary }]}>
            <Card.Content style={styles.statCardContent}>
              <View style={styles.statCardHeader}>
                <Ionicons name="business" size={24} color="white" />
                <Title style={styles.statNumber}>{stats.totalProjects || 0}</Title>
              </View>
              <Paragraph style={styles.statLabel}>Total Projects</Paragraph>
            </Card.Content>
          </Card>

          <Card style={[styles.statCard, { backgroundColor: theme.colors.success }]}>
            <Card.Content style={styles.statCardContent}>
              <View style={styles.statCardHeader}>
                <Ionicons name="checkmark-circle" size={24} color="white" />
                <Title style={styles.statNumber}>{stats.completedProjects || 0}</Title>
              </View>
              <Paragraph style={styles.statLabel}>Completed</Paragraph>
            </Card.Content>
          </Card>
        </View>

        <View style={styles.statsRow}>
          <Card style={[styles.statCard, { backgroundColor: theme.colors.warning }]}>
            <Card.Content style={styles.statCardContent}>
              <View style={styles.statCardHeader}>
                <Ionicons name="construct" size={24} color="white" />
                <Title style={styles.statNumber}>{stats.ongoingProjects || 0}</Title>
              </View>
              <Paragraph style={styles.statLabel}>Ongoing</Paragraph>
            </Card.Content>
          </Card>

          <Card style={[styles.statCard, { backgroundColor: theme.colors.info }]}>
            <Card.Content style={styles.statCardContent}>
              <View style={styles.statCardHeader}>
                <Ionicons name="mail" size={24} color="white" />
                <Title style={styles.statNumber}>{stats.newMessages || 0}</Title>
              </View>
              <Paragraph style={styles.statLabel}>New Messages</Paragraph>
            </Card.Content>
          </Card>
        </View>
      </View>

      {/* Recent Projects */}
      <Card style={styles.sectionCard}>
        <Card.Content>
          <View style={styles.sectionHeader}>
            <Title style={styles.sectionTitle}>Recent Projects</Title>
            <Button
              mode="text"
              onPress={() => navigation.navigate('Projects')}
              compact
            >
              View All
            </Button>
          </View>

          {recentProjects.length > 0 ? (
            recentProjects.map((project) => (
              <List.Item
                key={project.id}
                title={project.title}
                description={project.location}
                left={(props) => (
                  <Avatar.Icon
                    {...props}
                    icon="business"
                    style={{ backgroundColor: theme.colors.primary }}
                  />
                )}
                right={() => (
                  <Chip
                    style={{ backgroundColor: getStatusColor(project.status) }}
                    textStyle={{ color: 'white' }}
                  >
                    {project.status}
                  </Chip>
                )}
                onPress={() => navigation.navigate('ProjectDetail', { project })}
              />
            ))
          ) : (
            <Paragraph style={styles.emptyText}>No recent projects</Paragraph>
          )}
        </Card.Content>
      </Card>

      {/* Recent Messages */}
      <Card style={styles.sectionCard}>
        <Card.Content>
          <View style={styles.sectionHeader}>
            <Title style={styles.sectionTitle}>New Messages</Title>
            <Button
              mode="text"
              onPress={() => navigation.navigate('Messages')}
              compact
            >
              View All
            </Button>
          </View>

          {recentMessages.length > 0 ? (
            recentMessages.map((message) => (
              <List.Item
                key={message.id}
                title={message.name}
                description={message.subject || 'No subject'}
                left={(props) => (
                  <Avatar.Icon
                    {...props}
                    icon="account"
                    style={{ backgroundColor: theme.colors.accent }}
                  />
                )}
                right={() => (
                  <Paragraph style={styles.messageTime}>
                    {new Date(message.created_at).toLocaleDateString()}
                  </Paragraph>
                )}
                onPress={() => navigation.navigate('MessageDetail', { message })}
              />
            ))
          ) : (
            <Paragraph style={styles.emptyText}>No new messages</Paragraph>
          )}
        </Card.Content>
      </Card>

      {/* Quick Actions */}
      <Card style={styles.sectionCard}>
        <Card.Content>
          <Title style={styles.sectionTitle}>Quick Actions</Title>
          <View style={styles.quickActions}>
            <Button
              mode="contained"
              icon="plus"
              onPress={() => navigation.navigate('AddProject')}
              style={styles.quickActionButton}
            >
              Add Project
            </Button>
            <Button
              mode="contained"
              icon="plus"
              onPress={() => navigation.navigate('AddService')}
              style={styles.quickActionButton}
            >
              Add Service
            </Button>
          </View>

          {/* Debug button - remove in production */}
          <View style={styles.debugActions}>
            <Button
              mode="outlined"
              icon="bug"
              onPress={testConnection}
              style={styles.debugButton}
            >
              Test API Connection
            </Button>
          </View>
        </Card.Content>
      </Card>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: theme.spacing.md,
    fontSize: 16,
    color: theme.colors.text,
    textAlign: 'center',
  },
  header: {
    backgroundColor: theme.colors.primary,
    paddingTop: theme.spacing.lg,
    paddingBottom: theme.spacing.xl,
    paddingHorizontal: theme.spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  welcomeTitle: {
    color: 'white',
    fontSize: 18,
  },
  userName: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    fontWeight: 'bold',
  },
  logoutButton: {
    borderColor: 'white',
  },
  statsContainer: {
    padding: theme.spacing.md,
    marginTop: -theme.spacing.lg,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.md,
  },
  statCard: {
    flex: 1,
    marginHorizontal: theme.spacing.xs,
    ...theme.shadows.medium,
  },
  statCardContent: {
    padding: theme.spacing.md,
  },
  statCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },
  statNumber: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  statLabel: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
  },
  sectionCard: {
    margin: theme.spacing.md,
    marginTop: 0,
    ...theme.shadows.small,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  emptyText: {
    textAlign: 'center',
    color: theme.colors.placeholder,
    fontStyle: 'italic',
  },
  messageTime: {
    fontSize: 12,
    color: theme.colors.placeholder,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: theme.spacing.md,
  },
  quickActionButton: {
    flex: 1,
    marginHorizontal: theme.spacing.xs,
  },
  debugActions: {
    marginTop: theme.spacing.lg,
    paddingTop: theme.spacing.md,
    borderTopWidth: 1,
    borderTopColor: theme.colors.disabled,
  },
  debugButton: {
    borderColor: theme.colors.warning,
  },
});
