import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Linking,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { apiService } from '../services/apiService';
import { theme } from '../theme/theme';

export default function MessageDetailScreen({ route, navigation }) {
  const { message } = route.params;

  const getStatusColor = (status) => {
    switch (status) {
      case 'new':
        return theme.colors.warning;
      case 'read':
        return theme.colors.info;
      case 'replied':
        return theme.colors.success;
      default:
        return theme.colors.placeholder;
    }
  };

  const handleCall = () => {
    if (message.phone) {
      Linking.openURL(`tel:${message.phone}`);
    }
  };

  const handleEmail = () => {
    const emailUrl = `mailto:${message.email}?subject=Re: ${encodeURIComponent(message.subject || 'Your Inquiry')}`;
    Linking.openURL(emailUrl);
  };

  const markAsReplied = () => {
    Alert.alert(
      'Mark as Replied',
      'Mark this message as replied?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Mark as Replied', onPress: confirmMarkAsReplied },
      ]
    );
  };

  const confirmMarkAsReplied = async () => {
    try {
      console.log('✅ Marking message as replied:', message.id);
      const response = await apiService.updateMessage(message.id, { status: 'replied' });

      if (response.success) {
        Alert.alert('Success', 'Message marked as replied', [
          { text: 'OK', onPress: () => navigation.goBack() }
        ]);
      } else {
        Alert.alert('Error', response.message || 'Failed to update message status');
      }
    } catch (error) {
      console.error('Error updating message status:', error);
      Alert.alert('Error', 'Failed to update message status. Please try again.');
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.contactInfo}>
            <Text style={styles.contactName}>{message.name}</Text>
            <Text style={styles.contactEmail}>{message.email}</Text>
            {message.phone && (
              <Text style={styles.contactPhone}>{message.phone}</Text>
            )}
          </View>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(message.status) }]}>
            <Text style={styles.statusText}>{message.status}</Text>
          </View>
        </View>

        {/* Message Details */}
        <View style={styles.section}>
          <View style={styles.messageInfo}>
            <View style={styles.infoRow}>
              <Ionicons name="mail-outline" size={20} color={theme.colors.primary} />
              <Text style={styles.infoLabel}>Subject:</Text>
              <Text style={styles.infoValue}>{message.subject || 'No Subject'}</Text>
            </View>

            <View style={styles.infoRow}>
              <Ionicons name="calendar-outline" size={20} color={theme.colors.primary} />
              <Text style={styles.infoLabel}>Date:</Text>
              <Text style={styles.infoValue}>
                {new Date(message.created_at).toLocaleString()}
              </Text>
            </View>

            {message.phone && (
              <View style={styles.infoRow}>
                <Ionicons name="call-outline" size={20} color={theme.colors.primary} />
                <Text style={styles.infoLabel}>Phone:</Text>
                <TouchableOpacity onPress={handleCall}>
                  <Text style={[styles.infoValue, styles.linkText]}>{message.phone}</Text>
                </TouchableOpacity>
              </View>
            )}

            <View style={styles.infoRow}>
              <Ionicons name="person-outline" size={20} color={theme.colors.primary} />
              <Text style={styles.infoLabel}>Email:</Text>
              <TouchableOpacity onPress={handleEmail}>
                <Text style={[styles.infoValue, styles.linkText]}>{message.email}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Message Content */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Message</Text>
          <View style={styles.messageContent}>
            <Text style={styles.messageText}>{message.message}</Text>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.callButton} onPress={handleCall}>
            <Ionicons name="call" size={20} color="white" />
            <Text style={styles.buttonText}>Call</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.emailButton} onPress={handleEmail}>
            <Ionicons name="mail" size={20} color="white" />
            <Text style={styles.buttonText}>Reply</Text>
          </TouchableOpacity>

          {message.status !== 'replied' && (
            <TouchableOpacity style={styles.markButton} onPress={markAsReplied}>
              <Ionicons name="checkmark" size={20} color="white" />
              <Text style={styles.buttonText}>Mark Replied</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    padding: theme.spacing.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    backgroundColor: 'white',
    padding: theme.spacing.lg,
    borderRadius: theme.roundness,
    marginBottom: theme.spacing.lg,
    ...theme.shadows.small,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  contactEmail: {
    fontSize: 16,
    color: theme.colors.placeholder,
    marginBottom: 4,
  },
  contactPhone: {
    fontSize: 16,
    color: theme.colors.placeholder,
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: 20,
  },
  statusText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
    textTransform: 'capitalize',
  },
  section: {
    backgroundColor: 'white',
    padding: theme.spacing.md,
    borderRadius: theme.roundness,
    marginBottom: theme.spacing.lg,
    ...theme.shadows.small,
  },
  messageInfo: {
    marginBottom: theme.spacing.md,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  infoLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: theme.spacing.md,
    minWidth: 80,
  },
  infoValue: {
    fontSize: 16,
    color: theme.colors.text,
    flex: 1,
  },
  linkText: {
    color: theme.colors.primary,
    textDecorationLine: 'underline',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  messageContent: {
    backgroundColor: '#f8f9fa',
    padding: theme.spacing.md,
    borderRadius: theme.roundness,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
  },
  messageText: {
    fontSize: 16,
    color: theme.colors.text,
    lineHeight: 24,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: theme.spacing.lg,
  },
  callButton: {
    backgroundColor: theme.colors.success,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.roundness,
    flex: 1,
    marginRight: theme.spacing.xs,
    justifyContent: 'center',
  },
  emailButton: {
    backgroundColor: theme.colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.roundness,
    flex: 1,
    marginHorizontal: theme.spacing.xs,
    justifyContent: 'center',
  },
  markButton: {
    backgroundColor: theme.colors.info,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.roundness,
    flex: 1,
    marginLeft: theme.spacing.xs,
    justifyContent: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: theme.spacing.sm,
  },
});
