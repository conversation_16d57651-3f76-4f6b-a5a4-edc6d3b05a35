import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
  Image,
  Modal,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { apiService } from '../services/apiService';
import MediaUpload from '../components/MediaUpload';
import { theme } from '../theme/theme';

const { width } = Dimensions.get('window');
const imageSize = (width - theme.spacing.md * 3) / 2;

export default function MediaScreen({ navigation }) {
  const [media, setMedia] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState('all');
  const [selectedImage, setSelectedImage] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    loadMedia();
  }, [filter]);

  const loadMedia = async () => {
    try {
      setLoading(true);
      const typeFilter = filter === 'all' ? null : filter;
      const response = await apiService.getMedia({ type: typeFilter });
      if (response.success) {
        setMedia(response.data.media);
      } else {
        Alert.alert('Error', 'Failed to load media');
      }
    } catch (error) {
      console.error('Error loading media:', error);
      Alert.alert('Error', 'Failed to load media');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadMedia();
  };

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Please grant camera roll permissions to upload images.');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled) {
      uploadMedia(result.assets[0]);
    }
  };

  const takePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();

    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Please grant camera permissions to take photos.');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled) {
      uploadMedia(result.assets[0]);
    }
  };

  const uploadMedia = async (asset) => {
    try {
      const formData = new FormData();
      formData.append('file', {
        uri: asset.uri,
        type: asset.type === 'image' ? 'image/jpeg' : 'video/mp4',
        name: `media_${Date.now()}.${asset.type === 'image' ? 'jpg' : 'mp4'}`,
      });
      formData.append('title', `Mobile Upload ${new Date().toLocaleDateString()}`);
      formData.append('category', 'mobile');

      const response = await apiService.uploadMedia(formData);
      if (response.success) {
        Alert.alert('Success', 'Media uploaded successfully!');
        loadMedia();
      } else {
        Alert.alert('Error', response.message || 'Failed to upload media');
      }
    } catch (error) {
      console.error('Error uploading media:', error);
      Alert.alert('Error', 'Failed to upload media');
    }
  };

  const showUploadOptions = () => {
    Alert.alert(
      'Upload Media',
      'Choose an option',
      [
        { text: 'Camera', onPress: takePhoto },
        { text: 'Gallery', onPress: pickImage },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const openImageModal = (item) => {
    setSelectedImage(item);
    setModalVisible(true);
  };

  const deleteMedia = (item) => {
    Alert.alert(
      'Delete Media',
      'Are you sure you want to delete this media file?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => confirmDelete(item.id) },
      ]
    );
  };

  const confirmDelete = async (mediaId) => {
    try {
      const response = await apiService.deleteMedia(mediaId);
      if (response.success) {
        Alert.alert('Success', 'Media deleted successfully');
        loadMedia();
      } else {
        Alert.alert('Error', 'Failed to delete media');
      }
    } catch (error) {
      console.error('Error deleting media:', error);
      Alert.alert('Error', 'Failed to delete media');
    }
  };

  const renderMediaItem = ({ item }) => (
    <TouchableOpacity
      style={styles.mediaItem}
      onPress={() => openImageModal(item)}
      onLongPress={() => deleteMedia(item)}
    >
      {item.file_type === 'image' ? (
        <Image
          source={{ uri: `http://localhost/mobile-web-app/${item.file_path}` }}
          style={styles.mediaImage}
          resizeMode="cover"
        />
      ) : (
        <View style={styles.videoPlaceholder}>
          <Ionicons name="play-circle" size={40} color="white" />
        </View>
      )}

      <View style={styles.mediaOverlay}>
        <Text style={styles.mediaTitle} numberOfLines={1}>
          {item.title || 'Untitled'}
        </Text>
        <View style={styles.mediaInfo}>
          <Ionicons
            name={item.file_type === 'image' ? 'image' : 'videocam'}
            size={12}
            color="white"
          />
          {item.category && (
            <Text style={styles.categoryText}>{item.category}</Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const filterButtons = [
    { key: 'all', label: 'All', icon: 'apps' },
    { key: 'image', label: 'Photos', icon: 'image' },
    { key: 'video', label: 'Videos', icon: 'videocam' },
  ];

  if (loading && !refreshing) {
    return (
      <View style={styles.centerContainer}>
        <Text>Loading media...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Media Gallery</Text>
        <TouchableOpacity style={styles.addButton} onPress={showUploadOptions}>
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Enhanced Media Upload */}
      <View style={styles.uploadSection}>
        <MediaUpload
          allowMultiple={true}
          maxImages={5}
          onImageSelected={(images) => console.log('Images selected:', images)}
          onUploadComplete={(images) => {
            console.log('Upload completed:', images);
            loadMedia(); // Refresh the media list
          }}
          compressionQuality={0.8}
          aspectRatio={[16, 9]}
        />
      </View>

      {/* Filter Buttons */}
      <View style={styles.filterContainer}>
        {filterButtons.map((button) => (
          <TouchableOpacity
            key={button.key}
            style={[
              styles.filterButton,
              filter === button.key && styles.filterButtonActive
            ]}
            onPress={() => setFilter(button.key)}
          >
            <Ionicons
              name={button.icon}
              size={16}
              color={filter === button.key ? 'white' : theme.colors.primary}
            />
            <Text style={[
              styles.filterButtonText,
              filter === button.key && styles.filterButtonTextActive
            ]}>
              {button.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <FlatList
        data={media}
        renderItem={renderMediaItem}
        keyExtractor={(item) => item.id.toString()}
        numColumns={2}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="images-outline" size={64} color={theme.colors.placeholder} />
            <Text style={styles.emptyText}>No media files found</Text>
            <TouchableOpacity style={styles.emptyButton} onPress={showUploadOptions}>
              <Text style={styles.emptyButtonText}>Upload First Media</Text>
            </TouchableOpacity>
          </View>
        }
      />

      {/* Image Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <TouchableOpacity
            style={styles.modalBackground}
            onPress={() => setModalVisible(false)}
          >
            <View style={styles.modalContent}>
              {selectedImage && (
                <>
                  <Image
                    source={{ uri: `http://localhost/mobile-web-app/${selectedImage.file_path}` }}
                    style={styles.modalImage}
                    resizeMode="contain"
                  />
                  <View style={styles.modalInfo}>
                    <Text style={styles.modalTitle}>{selectedImage.title || 'Untitled'}</Text>
                    {selectedImage.alt_text && (
                      <Text style={styles.modalDescription}>{selectedImage.alt_text}</Text>
                    )}
                  </View>
                </>
              )}
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setModalVisible(false)}
          >
            <Ionicons name="close" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.md,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  addButton: {
    backgroundColor: theme.colors.primary,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  uploadSection: {
    backgroundColor: 'white',
    padding: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  filterContainer: {
    flexDirection: 'row',
    padding: theme.spacing.md,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: 20,
    marginRight: theme.spacing.sm,
    backgroundColor: '#f5f5f5',
  },
  filterButtonActive: {
    backgroundColor: theme.colors.primary,
  },
  filterButtonText: {
    fontSize: 14,
    color: theme.colors.text,
    marginLeft: theme.spacing.xs,
  },
  filterButtonTextActive: {
    color: 'white',
    fontWeight: 'bold',
  },
  listContainer: {
    padding: theme.spacing.md,
  },
  mediaItem: {
    width: imageSize,
    height: imageSize,
    marginRight: theme.spacing.md,
    marginBottom: theme.spacing.md,
    borderRadius: theme.roundness,
    overflow: 'hidden',
    backgroundColor: '#f5f5f5',
  },
  mediaImage: {
    width: '100%',
    height: '100%',
  },
  videoPlaceholder: {
    width: '100%',
    height: '100%',
    backgroundColor: '#333',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: theme.spacing.sm,
  },
  mediaTitle: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  mediaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryText: {
    color: 'white',
    fontSize: 10,
    marginLeft: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 18,
    color: theme.colors.placeholder,
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.lg,
  },
  emptyButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.roundness,
  },
  emptyButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalBackground: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
  },
  modalImage: {
    width: '100%',
    height: 300,
  },
  modalInfo: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: theme.spacing.md,
    borderBottomLeftRadius: theme.roundness,
    borderBottomRightRadius: theme.roundness,
  },
  modalTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: theme.spacing.sm,
  },
  modalDescription: {
    color: 'white',
    fontSize: 14,
    lineHeight: 20,
  },
  closeButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
