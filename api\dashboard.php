<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/auth.php';

$method = $_SERVER['REQUEST_METHOD'];

// Debug logging
error_log("Dashboard API called with method: " . $method);
error_log("Headers: " . json_encode(getallheaders()));

if ($method !== 'GET') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

try {
    $user = requireAuth();
    error_log("User authenticated: " . json_encode($user));
} catch (Exception $e) {
    error_log("Authentication failed: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Authentication failed: ' . $e->getMessage()], 401);
}

$database = new Database();
$conn = $database->getConnection();

try {
    // Get dashboard statistics
    error_log("Attempting to get dashboard stats");
    $stats = getDashboardStats($conn);
    error_log("Dashboard stats retrieved successfully");

    jsonResponse([
        'success' => true,
        'data' => $stats
    ]);

} catch (Exception $e) {
    error_log("Error in dashboard API: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    jsonResponse(['success' => false, 'message' => 'Error fetching dashboard data: ' . $e->getMessage()], 500);
}

function getDashboardStats($conn) {
    $stats = [];
    
    try {
        // Projects statistics
        $stmt = $conn->prepare("SELECT 
            COUNT(*) as total_projects,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_projects,
            SUM(CASE WHEN status = 'ongoing' THEN 1 ELSE 0 END) as ongoing_projects,
            SUM(CASE WHEN status = 'planned' THEN 1 ELSE 0 END) as planned_projects
            FROM projects");
        $stmt->execute();
        $projectStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Services statistics
        $stmt = $conn->prepare("SELECT 
            COUNT(*) as total_services,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_services
            FROM services");
        $stmt->execute();
        $serviceStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Messages statistics
        $stmt = $conn->prepare("SELECT 
            COUNT(*) as total_messages,
            SUM(CASE WHEN status = 'new' THEN 1 ELSE 0 END) as new_messages,
            SUM(CASE WHEN status = 'read' THEN 1 ELSE 0 END) as read_messages
            FROM messages");
        $stmt->execute();
        $messageStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Media statistics
        $stmt = $conn->prepare("SELECT 
            COUNT(*) as total_media,
            SUM(CASE WHEN file_type = 'image' THEN 1 ELSE 0 END) as total_images,
            SUM(CASE WHEN file_type = 'video' THEN 1 ELSE 0 END) as total_videos
            FROM media");
        $stmt->execute();
        $mediaStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Recent activity - last 7 days
        $stmt = $conn->prepare("SELECT 
            COUNT(*) as recent_projects
            FROM projects 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
        $stmt->execute();
        $recentProjects = $stmt->fetch(PDO::FETCH_ASSOC)['recent_projects'];
        
        $stmt = $conn->prepare("SELECT 
            COUNT(*) as recent_messages
            FROM messages 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
        $stmt->execute();
        $recentMessages = $stmt->fetch(PDO::FETCH_ASSOC)['recent_messages'];
        
        // Get recent projects (last 5)
        $stmt = $conn->prepare("SELECT id, title, location, status, created_at 
            FROM projects 
            ORDER BY created_at DESC 
            LIMIT 5");
        $stmt->execute();
        $recentProjectsList = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get recent messages (last 5)
        $stmt = $conn->prepare("SELECT id, name, email, subject, status, created_at 
            FROM messages 
            ORDER BY created_at DESC 
            LIMIT 5");
        $stmt->execute();
        $recentMessagesList = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Compile all statistics
        $stats = [
            'projects' => [
                'total' => (int)$projectStats['total_projects'],
                'completed' => (int)$projectStats['completed_projects'],
                'ongoing' => (int)$projectStats['ongoing_projects'],
                'planned' => (int)$projectStats['planned_projects'],
                'recent' => (int)$recentProjects
            ],
            'services' => [
                'total' => (int)$serviceStats['total_services'],
                'active' => (int)$serviceStats['active_services']
            ],
            'messages' => [
                'total' => (int)$messageStats['total_messages'],
                'new' => (int)$messageStats['new_messages'],
                'read' => (int)$messageStats['read_messages'],
                'recent' => (int)$recentMessages
            ],
            'media' => [
                'total' => (int)$mediaStats['total_media'],
                'images' => (int)$mediaStats['total_images'],
                'videos' => (int)$mediaStats['total_videos']
            ],
            'recent_projects' => $recentProjectsList,
            'recent_messages' => $recentMessagesList,
            'last_updated' => date('Y-m-d H:i:s')
        ];
        
    } catch (PDOException $e) {
        throw new Exception('Database error: ' . $e->getMessage());
    }
    
    return $stats;
}
?>
