import axios from 'axios';
import NetInfo from '@react-native-community/netinfo';
import { config, networkConfig, buildApiUrl } from '../config/environment';

class ApiService {
  constructor() {
    this.authToken = null;
    this.isOnline = true;
    this.requestQueue = [];

    // Initialize network monitoring
    this.initializeNetworkMonitoring();

    this.api = axios.create({
      baseURL: config.apiUrl,
      timeout: networkConfig.timeout,
      headers: networkConfig.headers,
    });

    // Request interceptor to add auth token and handle offline requests
    this.api.interceptors.request.use(
      async (config) => {
        // Check network connectivity
        if (!this.isOnline) {
          throw new NetworkError('No internet connection');
        }

        // Add auth token
        if (this.authToken) {
          config.headers.Authorization = `Bearer ${this.authToken}`;
          if (config.isDevelopment) {
            console.log(`🔐 Adding auth token to ${config.method?.toUpperCase()} ${config.url}`);
          }
        } else {
          if (config.isDevelopment) {
            console.log(`⚠️ No auth token available for ${config.method?.toUpperCase()} ${config.url}`);
          }
        }

        // Add request metadata
        config.metadata = {
          startTime: Date.now(),
          retryCount: config.retryCount || 0,
        };

        return config;
      },
      (error) => {
        return Promise.reject(this.handleError(error));
      }
    );

    // Response interceptor with enhanced error handling
    this.api.interceptors.response.use(
      (response) => {
        // Log successful requests in development
        if (config.isDevelopment) {
          const duration = Date.now() - response.config.metadata.startTime;
          console.log(`✅ API Success: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`);
        }
        return response.data;
      },
      async (error) => {
        return this.handleErrorWithRetry(error);
      }
    );
  }

  // Initialize network monitoring
  initializeNetworkMonitoring() {
    NetInfo.addEventListener(state => {
      const wasOnline = this.isOnline;
      this.isOnline = state.isConnected && state.isInternetReachable;

      if (config.isDevelopment) {
        console.log(`🌐 Network status: ${this.isOnline ? 'Online' : 'Offline'}`);
      }

      // Process queued requests when coming back online
      if (!wasOnline && this.isOnline && this.requestQueue.length > 0) {
        this.processRequestQueue();
      }
    });
  }

  setAuthToken(token) {
    this.authToken = token;
    if (config.isDevelopment) {
      console.log('🔑 Auth token set:', token ? `${token.substring(0, 20)}...` : 'null');
    }
  }

  // Enhanced error handling with retry logic
  async handleErrorWithRetry(error) {
    const config = error.config;
    const retryCount = config.retryCount || 0;
    const maxRetries = networkConfig.retry.attempts;

    // Log error in development
    if (config.isDevelopment) {
      console.log(`❌ API Error: ${error.message}`, {
        url: config.url,
        status: error.response?.status,
        retryCount,
      });
    }

    // Don't retry certain errors
    if (this.shouldNotRetry(error)) {
      return Promise.reject(this.handleError(error));
    }

    // Retry if we haven't exceeded max attempts
    if (retryCount < maxRetries) {
      const delay = this.calculateRetryDelay(retryCount);

      if (config.isDevelopment) {
        console.log(`🔄 Retrying request in ${delay}ms (attempt ${retryCount + 1}/${maxRetries})`);
      }

      await this.sleep(delay);

      // Increment retry count and retry
      config.retryCount = retryCount + 1;
      return this.api.request(config);
    }

    // Max retries exceeded
    return Promise.reject(this.handleError(error));
  }

  // Determine if error should not be retried
  shouldNotRetry(error) {
    const status = error.response?.status;

    // Don't retry client errors (4xx) except 408 (timeout) and 429 (rate limit)
    if (status >= 400 && status < 500 && status !== 408 && status !== 429) {
      return true;
    }

    // Don't retry network errors if offline
    if (!this.isOnline) {
      return true;
    }

    return false;
  }

  // Calculate retry delay with exponential backoff
  calculateRetryDelay(retryCount) {
    const baseDelay = networkConfig.retry.delay;
    const multiplier = networkConfig.retry.backoffMultiplier;
    const maxDelay = networkConfig.retry.maxDelay;

    const delay = baseDelay * Math.pow(multiplier, retryCount);
    return Math.min(delay, maxDelay);
  }

  // Sleep utility
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Enhanced error handling
  handleError(error) {
    if (error instanceof NetworkError) {
      return error;
    }

    const status = error.response?.status;
    const message = error.response?.data?.message || error.message;

    // Handle specific HTTP status codes
    switch (status) {
      case 401:
        this.authToken = null;
        return new AuthenticationError('Authentication failed. Please login again.');

      case 403:
        return new AuthorizationError('You do not have permission to perform this action.');

      case 404:
        return new NotFoundError('The requested resource was not found.');

      case 408:
        return new TimeoutError('Request timed out. Please try again.');

      case 429:
        return new RateLimitError('Too many requests. Please wait and try again.');

      case 500:
        return new ServerError('Server error occurred. Please try again later.');

      case 503:
        return new ServiceUnavailableError('Service is temporarily unavailable.');

      default:
        if (!this.isOnline) {
          return new NetworkError('No internet connection. Please check your network and try again.');
        }

        return new ApiError(message || 'An unexpected error occurred.');
    }
  }

  // Process queued requests when coming back online
  async processRequestQueue() {
    if (config.isDevelopment) {
      console.log(`📤 Processing ${this.requestQueue.length} queued requests`);
    }

    const queue = [...this.requestQueue];
    this.requestQueue = [];

    for (const request of queue) {
      try {
        await this.api.request(request);
      } catch (error) {
        console.error('Failed to process queued request:', error);
      }
    }
  }

  // Authentication endpoints
  async login(username, password) {
    return this.api.post('/auth.php?action=login', {
      username,
      password,
    });
  }

  async logout() {
    return this.api.post('/auth.php?action=logout');
  }

  async verifyToken() {
    return this.api.get('/auth.php?action=verify');
  }

  async refreshToken() {
    return this.api.post('/auth.php?action=refresh');
  }

  async updateProfile(profileData) {
    return this.api.put('/auth.php?action=update_profile', profileData);
  }

  // Projects endpoints
  async getProjects(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.api.get(`/projects.php?${queryString}`);
  }

  async getProject(id) {
    return this.api.get(`/projects.php?id=${id}`);
  }

  async createProject(projectData) {
    return this.api.post('/projects.php', projectData);
  }

  async updateProject(id, projectData) {
    return this.api.put(`/projects.php?id=${id}`, projectData);
  }

  async deleteProject(id) {
    return this.api.delete(`/projects.php?id=${id}`);
  }

  // Services endpoints
  async getServices(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.api.get(`/services.php?${queryString}`);
  }

  async getService(id) {
    return this.api.get(`/services.php?id=${id}`);
  }

  async createService(serviceData) {
    return this.api.post('/services.php', serviceData);
  }

  async updateService(id, serviceData) {
    return this.api.put(`/services.php?id=${id}`, serviceData);
  }

  async deleteService(id) {
    return this.api.delete(`/services.php?id=${id}`);
  }

  // Messages endpoints
  async getMessages(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.api.get(`/messages.php?${queryString}`);
  }

  async getMessage(id) {
    return this.api.get(`/messages.php?id=${id}`);
  }

  async updateMessage(id, messageData) {
    return this.api.put(`/messages.php?id=${id}`, messageData);
  }

  async deleteMessage(id) {
    return this.api.delete(`/messages.php?id=${id}`);
  }

  async sendContactMessage(messageData) {
    return this.api.post('/messages.php?action=contact', messageData);
  }

  // Media endpoints
  async getMedia(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.api.get(`/media.php?${queryString}`);
  }

  async uploadMedia(formData) {
    return this.api.post('/media.php', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  async deleteMedia(id) {
    return this.api.delete(`/media.php?id=${id}`);
  }

  // Dashboard/Statistics endpoints
  async getDashboardStats() {
    try {
      // Try the authenticated dashboard first
      return await this.api.get('/dashboard.php');
    } catch (error) {
      console.log('Authenticated dashboard failed, trying simple dashboard:', error.message);

      // Fallback to simple dashboard for testing
      try {
        return await this.api.get('/dashboard-simple.php');
      } catch (fallbackError) {
        console.log('Simple dashboard also failed:', fallbackError.message);
        throw this.handleError(error); // Throw original error
      }
    }
  }

  // Test endpoint for debugging
  async testConnection() {
    try {
      return await this.api.get('/test.php');
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Debug endpoint for detailed debugging
  async debugConnection() {
    try {
      return await this.api.get('/debug.php');
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get current auth status
  getAuthStatus() {
    return {
      hasToken: !!this.authToken,
      tokenPreview: this.authToken ? `${this.authToken.substring(0, 20)}...` : null,
      tokenLength: this.authToken ? this.authToken.length : 0,
    };
  }

  // Network status methods
  isConnected() {
    return this.isOnline;
  }

  // Get network info
  async getNetworkInfo() {
    try {
      const netInfo = await NetInfo.fetch();
      return {
        isConnected: netInfo.isConnected,
        isInternetReachable: netInfo.isInternetReachable,
        type: netInfo.type,
        details: netInfo.details,
      };
    } catch (error) {
      return {
        isConnected: false,
        isInternetReachable: false,
        type: 'unknown',
        details: null,
      };
    }
  }

  // Utility method for handling file uploads
  createFormData(data, fileField = null, fileUri = null) {
    const formData = new FormData();

    // Add regular fields
    Object.keys(data).forEach(key => {
      if (data[key] !== null && data[key] !== undefined) {
        formData.append(key, data[key]);
      }
    });

    // Add file if provided
    if (fileField && fileUri) {
      const filename = fileUri.split('/').pop();
      const match = /\.(\w+)$/.exec(filename);
      const type = match ? `image/${match[1]}` : 'image';

      formData.append(fileField, {
        uri: fileUri,
        name: filename,
        type,
      });
    }

    return formData;
  }
}

// Custom Error Classes
class ApiError extends Error {
  constructor(message, code = null) {
    super(message);
    this.name = 'ApiError';
    this.code = code;
  }
}

class NetworkError extends ApiError {
  constructor(message = 'Network error occurred') {
    super(message);
    this.name = 'NetworkError';
  }
}

class AuthenticationError extends ApiError {
  constructor(message = 'Authentication failed') {
    super(message);
    this.name = 'AuthenticationError';
  }
}

class AuthorizationError extends ApiError {
  constructor(message = 'Authorization failed') {
    super(message);
    this.name = 'AuthorizationError';
  }
}

class NotFoundError extends ApiError {
  constructor(message = 'Resource not found') {
    super(message);
    this.name = 'NotFoundError';
  }
}

class TimeoutError extends ApiError {
  constructor(message = 'Request timed out') {
    super(message);
    this.name = 'TimeoutError';
  }
}

class RateLimitError extends ApiError {
  constructor(message = 'Rate limit exceeded') {
    super(message);
    this.name = 'RateLimitError';
  }
}

class ServerError extends ApiError {
  constructor(message = 'Server error') {
    super(message);
    this.name = 'ServerError';
  }
}

class ServiceUnavailableError extends ApiError {
  constructor(message = 'Service unavailable') {
    super(message);
    this.name = 'ServiceUnavailableError';
  }
}

// Export error classes for use in components
export {
  ApiError,
  NetworkError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  TimeoutError,
  RateLimitError,
  ServerError,
  ServiceUnavailableError,
};

export const apiService = new ApiService();
