<?php
require_once __DIR__ . '/../includes/functions.php';
requireLogin();

$pageTitle = 'Media Management';

// Handle file upload and edit
$message = '';
$messageType = '';

// Handle media edit
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'edit') {
    $mediaId = (int)$_POST['media_id'];
    $title = sanitizeInput($_POST['title'] ?? '');
    $altText = sanitizeInput($_POST['alt_text'] ?? '');
    $category = sanitizeInput($_POST['category'] ?? '');

    if (empty($title)) {
        $title = 'Untitled Media';
    }

    if ($mediaId > 0) {
        $database = new Database();
        $conn = $database->getConnection();

        try {
            $stmt = $conn->prepare("UPDATE media SET title = ?, alt_text = ?, category = ? WHERE id = ?");
            $stmt->execute([$title, $altText, $category, $mediaId]);

            $message = 'Media updated successfully!';
            $messageType = 'success';
        } catch (PDOException $e) {
            $message = 'Database error: ' . $e->getMessage();
            $messageType = 'error';
        }
    } else {
        $message = 'Invalid media ID!';
        $messageType = 'error';
    }
}

// Handle file upload
if ($_POST && isset($_FILES['file'])) {
    // Validate form inputs
    $title = sanitizeInput($_POST['title'] ?? '');
    $altText = sanitizeInput($_POST['alt_text'] ?? '');
    $category = sanitizeInput($_POST['category'] ?? '');

    if (empty($title)) {
        $title = 'Untitled Media';
    }

    $uploadResult = uploadFile($_FILES['file'], '../uploads/');

    if ($uploadResult['success']) {
        $database = new Database();
        $conn = $database->getConnection();

        // Determine file type from MIME type
        $fileType = strpos($uploadResult['type'], 'image/') === 0 ? 'image' : 'video';

        try {
            $stmt = $conn->prepare("INSERT INTO media (title, filename, file_path, file_type, file_size, alt_text, category) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                $title,
                $uploadResult['filename'],
                $uploadResult['relative_path'],
                $fileType,
                $uploadResult['size'],
                $altText,
                $category
            ]);

            $message = 'Media uploaded successfully!';
            $messageType = 'success';

            // Clear form data after successful upload
            $_POST = [];

        } catch (PDOException $e) {
            $message = 'Database error: ' . $e->getMessage();
            $messageType = 'error';

            // Delete uploaded file if database insert fails
            if (file_exists($uploadResult['path'])) {
                unlink($uploadResult['path']);
            }
        }
    } else {
        $message = $uploadResult['message'];
        $messageType = 'error';
    }
}

// Handle delete request
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $mediaId = (int)$_GET['delete'];
    $database = new Database();
    $conn = $database->getConnection();

    try {
        // Get file path before deleting from database
        $stmt = $conn->prepare("SELECT file_path FROM media WHERE id = ?");
        $stmt->execute([$mediaId]);
        $media = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($media) {
            // Delete from database
            $stmt = $conn->prepare("DELETE FROM media WHERE id = ?");
            $stmt->execute([$mediaId]);

            // Delete physical file
            $filePath = '../' . $media['file_path'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            $message = 'Media deleted successfully!';
            $messageType = 'success';
        } else {
            $message = 'Media not found!';
            $messageType = 'error';
        }
    } catch (PDOException $e) {
        $message = 'Error deleting media: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// Get all media
$allMedia = getMedia();

include 'includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Media Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                        <i class="fas fa-upload"></i> Upload Media
                    </button>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType == 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                    <i class="fas fa-<?php echo $messageType == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Media Grid -->
            <div class="row">
                <?php if (!empty($allMedia)): ?>
                    <?php foreach ($allMedia as $media): ?>
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card media-card">
                                <div class="media-preview">
                                    <?php if ($media['file_type'] == 'image'): ?>
                                        <img src="../<?php echo htmlspecialchars($media['file_path']); ?>" 
                                             alt="<?php echo htmlspecialchars($media['alt_text']); ?>" 
                                             class="card-img-top">
                                    <?php else: ?>
                                        <video class="card-img-top" controls>
                                            <source src="../<?php echo htmlspecialchars($media['file_path']); ?>" type="video/mp4">
                                        </video>
                                    <?php endif; ?>
                                    <div class="media-overlay">
                                        <div class="media-actions">
                                            <button class="btn btn-sm btn-light" onclick="editMedia(<?php echo $media['id']; ?>, '<?php echo htmlspecialchars($media['title'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($media['alt_text'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($media['category'], ENT_QUOTES); ?>', '<?php echo $media['file_type']; ?>', '<?php echo htmlspecialchars($media['file_path'], ENT_QUOTES); ?>')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="deleteMedia(<?php echo $media['id']; ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <h6 class="card-title"><?php echo htmlspecialchars($media['title'] ?: 'Untitled'); ?></h6>
                                    <p class="card-text small text-muted">
                                        <?php echo htmlspecialchars($media['alt_text']); ?>
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="badge bg-<?php echo $media['file_type'] == 'image' ? 'primary' : 'success'; ?>">
                                            <?php echo ucfirst($media['file_type']); ?>
                                        </span>
                                        <?php if ($media['category']): ?>
                                            <span class="badge bg-secondary"><?php echo htmlspecialchars($media['category']); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo date('M d, Y', strtotime($media['uploaded_at'])); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-images fa-3x text-muted mb-3"></i>
                        <h4>No Media Files</h4>
                        <p class="text-muted">Upload your first media file to get started.</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                            <i class="fas fa-upload"></i> Upload Media
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload Media</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="file" class="form-label">Select File</label>
                        <input type="file" class="form-control" id="file" name="file"
                               accept="image/jpeg,image/jpg,image/png,image/gif,image/webp,video/mp4,video/quicktime,video/x-msvideo"
                               required onchange="previewFile()">
                        <div class="form-text">Supported formats: JPG, PNG, GIF, WebP, MP4, MOV, AVI (Max 50MB)</div>
                        <div id="filePreview" class="mt-3" style="display: none;">
                            <img id="imagePreview" style="max-width: 100%; max-height: 200px; display: none;" />
                            <video id="videoPreview" controls style="max-width: 100%; max-height: 200px; display: none;"></video>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="title" class="form-label">Title</label>
                        <input type="text" class="form-control" id="title" name="title" 
                               placeholder="Enter media title">
                    </div>
                    <div class="mb-3">
                        <label for="alt_text" class="form-label">Description</label>
                        <textarea class="form-control" id="alt_text" name="alt_text" rows="3" 
                                  placeholder="Enter media description"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="category" class="form-label">Category</label>
                        <select class="form-control" id="category" name="category">
                            <option value="">Select Category</option>
                            <option value="projects">Projects</option>
                            <option value="groundworks">Groundworks</option>
                            <option value="rc-frames">RC Frames</option>
                            <option value="basements">Basements</option>
                            <option value="landscaping">Landscaping</option>
                            <option value="team">Team</option>
                            <option value="equipment">Equipment</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Upload</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Media Modal -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Media</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editForm">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="media_id" id="editMediaId">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Current Media</label>
                        <div id="editMediaPreview" class="text-center mb-3">
                            <!-- Media preview will be inserted here -->
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editTitle" class="form-label">Title</label>
                        <input type="text" class="form-control" id="editTitle" name="title"
                               placeholder="Enter media title" required>
                    </div>
                    <div class="mb-3">
                        <label for="editAltText" class="form-label">Description</label>
                        <textarea class="form-control" id="editAltText" name="alt_text" rows="3"
                                  placeholder="Enter media description"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="editCategory" class="form-label">Category</label>
                        <select class="form-control" id="editCategory" name="category">
                            <option value="">Select Category</option>
                            <option value="projects">Projects</option>
                            <option value="groundworks">Groundworks</option>
                            <option value="rc-frames">RC Frames</option>
                            <option value="basements">Basements</option>
                            <option value="landscaping">Landscaping</option>
                            <option value="team">Team</option>
                            <option value="equipment">Equipment</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Media</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.media-card {
    transition: transform 0.3s ease;
    height: 100%;
}

.media-card:hover {
    transform: translateY(-5px);
}

.media-preview {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.media-preview img,
.media-preview video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.media-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.media-card:hover .media-overlay {
    opacity: 1;
}

.media-actions {
    display: flex;
    gap: 0.5rem;
}
</style>

<script>
function editMedia(id, title, altText, category, fileType, filePath) {
    // Populate the edit form
    document.getElementById('editMediaId').value = id;
    document.getElementById('editTitle').value = title === 'Untitled' ? '' : title;
    document.getElementById('editAltText').value = altText;
    document.getElementById('editCategory').value = category;

    // Show media preview
    const previewDiv = document.getElementById('editMediaPreview');
    if (fileType === 'image') {
        previewDiv.innerHTML = `<img src="../${filePath}" alt="${altText}" style="max-width: 100%; max-height: 150px; border-radius: 8px;">`;
    } else if (fileType === 'video') {
        previewDiv.innerHTML = `<video controls style="max-width: 100%; max-height: 150px; border-radius: 8px;"><source src="../${filePath}" type="video/mp4"></video>`;
    }

    // Show the modal
    const editModal = new bootstrap.Modal(document.getElementById('editModal'));
    editModal.show();
}

function deleteMedia(id) {
    if (confirm('Are you sure you want to delete this media file? This action cannot be undone.')) {
        window.location.href = 'media.php?delete=' + id;
    }
}

function previewFile() {
    const fileInput = document.getElementById('file');
    const filePreview = document.getElementById('filePreview');
    const imagePreview = document.getElementById('imagePreview');
    const videoPreview = document.getElementById('videoPreview');
    const titleInput = document.getElementById('title');

    if (fileInput.files && fileInput.files[0]) {
        const file = fileInput.files[0];
        const fileSize = file.size;
        const maxSize = 50 * 1024 * 1024; // 50MB

        // Check file size
        if (fileSize > maxSize) {
            alert('File is too large. Maximum size is 50MB.');
            fileInput.value = '';
            filePreview.style.display = 'none';
            return;
        }

        // Auto-fill title if empty
        if (!titleInput.value) {
            const fileName = file.name.replace(/\.[^/.]+$/, ""); // Remove extension
            titleInput.value = fileName.replace(/[_-]/g, ' '); // Replace underscores and hyphens with spaces
        }

        const reader = new FileReader();

        reader.onload = function(e) {
            filePreview.style.display = 'block';

            if (file.type.startsWith('image/')) {
                imagePreview.src = e.target.result;
                imagePreview.style.display = 'block';
                videoPreview.style.display = 'none';
            } else if (file.type.startsWith('video/')) {
                videoPreview.src = e.target.result;
                videoPreview.style.display = 'block';
                imagePreview.style.display = 'none';
            }
        };

        reader.readAsDataURL(file);
    } else {
        filePreview.style.display = 'none';
    }
}

// Reset form when modal is closed
document.getElementById('uploadModal').addEventListener('hidden.bs.modal', function () {
    document.querySelector('#uploadModal form').reset();
    document.getElementById('filePreview').style.display = 'none';
    document.getElementById('imagePreview').style.display = 'none';
    document.getElementById('videoPreview').style.display = 'none';
});

// Form validation for upload
document.querySelector('#uploadModal form').addEventListener('submit', function(e) {
    const fileInput = document.getElementById('file');
    const titleInput = document.getElementById('title');

    if (!fileInput.files || !fileInput.files[0]) {
        e.preventDefault();
        alert('Please select a file to upload.');
        return;
    }

    if (!titleInput.value.trim()) {
        titleInput.value = 'Untitled Media';
    }

    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Uploading...';
    submitBtn.disabled = true;

    // Re-enable button after a delay (in case of errors)
    setTimeout(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }, 10000);
});

// Form validation for edit
document.querySelector('#editForm').addEventListener('submit', function(e) {
    const titleInput = document.getElementById('editTitle');

    if (!titleInput.value.trim()) {
        titleInput.value = 'Untitled Media';
    }

    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Updating...';
    submitBtn.disabled = true;

    // Re-enable button after a delay (in case of errors)
    setTimeout(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }, 10000);
});

// Reset edit form when modal is closed
document.getElementById('editModal').addEventListener('hidden.bs.modal', function () {
    document.getElementById('editForm').reset();
    document.getElementById('editMediaPreview').innerHTML = '';
});
</script>

<?php include 'includes/admin_footer.php'; ?>
