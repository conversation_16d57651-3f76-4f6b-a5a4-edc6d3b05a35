<?php
require_once __DIR__ . '/../includes/functions.php';
requireLogin();

$pageTitle = 'Add New Service';

$message = '';
$messageType = '';

if ($_POST) {
    $title = sanitizeInput($_POST['title']);
    $description = sanitizeInput($_POST['description']);
    $icon = sanitizeInput($_POST['icon']);
    $status = sanitizeInput($_POST['status']);
    
    if ($title && $description) {
        $database = new Database();
        $conn = $database->getConnection();
        
        // Handle service image upload
        $serviceImage = null;
        if (isset($_FILES['service_image']) && $_FILES['service_image']['error'] === UPLOAD_ERR_OK) {
            $uploadResult = uploadFile($_FILES['service_image'], '../uploads/');
            if ($uploadResult['success']) {
                $serviceImage = $uploadResult['relative_path'];
            }
        }
        
        try {
            $stmt = $conn->prepare("INSERT INTO services (title, description, image, icon, status) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$title, $description, $serviceImage, $icon, $status]);
            
            $message = 'Service added successfully!';
            $messageType = 'success';
            
            // Clear form data
            $_POST = [];
        } catch (PDOException $e) {
            $message = 'Database error: ' . $e->getMessage();
            $messageType = 'error';
        }
    } else {
        $message = 'Please fill in all required fields.';
        $messageType = 'error';
    }
}

// Common FontAwesome icons for services
$commonIcons = [
    'fas fa-tools' => 'Tools',
    'fas fa-hard-hat' => 'Hard Hat',
    'fas fa-hammer' => 'Hammer',
    'fas fa-wrench' => 'Wrench',
    'fas fa-cogs' => 'Cogs',
    'fas fa-building' => 'Building',
    'fas fa-home' => 'Home',
    'fas fa-industry' => 'Industry',
    'fas fa-truck' => 'Truck',
    'fas fa-tractor' => 'Tractor',
    'fas fa-paint-roller' => 'Paint Roller',
    'fas fa-ruler-combined' => 'Ruler',
    'fas fa-drafting-compass' => 'Compass',
    'fas fa-layer-group' => 'Layers'
];

include 'includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Add New Service</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="services.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Services
                    </a>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType == 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                    <i class="fas fa-<?php echo $messageType == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Add Service Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Service Information</h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="title" class="form-label">Service Title *</label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="<?php echo isset($_POST['title']) ? htmlspecialchars($_POST['title']) : ''; ?>" 
                                           placeholder="e.g., Civil Engineering" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description *</label>
                                    <textarea class="form-control" id="description" name="description" rows="6" 
                                              placeholder="Describe the service in detail..." required><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="icon" class="form-label">Icon</label>
                                            <select class="form-control" id="icon" name="icon" onchange="updateIconPreview()">
                                                <option value="">Select an icon</option>
                                                <?php foreach ($commonIcons as $iconClass => $iconName): ?>
                                                    <option value="<?php echo $iconClass; ?>" 
                                                            <?php echo (isset($_POST['icon']) && $_POST['icon'] == $iconClass) ? 'selected' : ''; ?>>
                                                        <?php echo $iconName; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="form-text">Choose an icon to represent this service</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">Status</label>
                                            <select class="form-control" id="status" name="status">
                                                <option value="active" <?php echo (isset($_POST['status']) && $_POST['status'] == 'active') ? 'selected' : ''; ?>>Active</option>
                                                <option value="inactive" <?php echo (isset($_POST['status']) && $_POST['status'] == 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Icon Preview</label>
                                    <div class="text-center p-4 border rounded">
                                        <i id="iconPreview" class="fas fa-tools fa-3x text-primary"></i>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="service_image" class="form-label">Service Image</label>
                                    <input type="file" class="form-control" id="service_image" name="service_image" 
                                           accept="image/*" onchange="previewImage()">
                                    <div class="form-text">Upload an image for this service</div>
                                    <div id="imagePreview" class="mt-3" style="display: none;">
                                        <img id="preview" style="max-width: 100%; max-height: 200px; border-radius: 8px;" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="services.php" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Service
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
function updateIconPreview() {
    const iconSelect = document.getElementById('icon');
    const iconPreview = document.getElementById('iconPreview');
    
    if (iconSelect.value) {
        iconPreview.className = iconSelect.value + ' fa-3x text-primary';
    } else {
        iconPreview.className = 'fas fa-tools fa-3x text-primary';
    }
}

function previewImage() {
    const fileInput = document.getElementById('service_image');
    const imagePreview = document.getElementById('imagePreview');
    const preview = document.getElementById('preview');
    
    if (fileInput.files && fileInput.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            preview.src = e.target.result;
            imagePreview.style.display = 'block';
        };
        
        reader.readAsDataURL(fileInput.files[0]);
    } else {
        imagePreview.style.display = 'none';
    }
}

// Initialize icon preview
document.addEventListener('DOMContentLoaded', function() {
    updateIconPreview();
});
</script>

<?php include 'includes/admin_footer.php'; ?>
