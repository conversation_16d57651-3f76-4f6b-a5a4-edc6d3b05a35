{"name": "FloriConstructionAdmin", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-community/netinfo": "^11.1.0", "@react-navigation/bottom-tabs": "^6.5.7", "@react-navigation/native": "^6.1.6", "@react-navigation/native-stack": "^6.9.12", "axios": "^1.4.0", "expo": "~49.0.0", "expo-camera": "~13.2.1", "expo-constants": "~14.2.1", "expo-image-picker": "~14.1.1", "expo-secure-store": "~12.1.1", "expo-status-bar": "~1.4.4", "react": "18.2.0", "react-native": "0.72.6", "react-native-paper": "^5.8.0", "react-native-safe-area-context": "4.5.0", "react-native-screens": "~3.20.0", "react-native-vector-icons": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}