<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../includes/functions.php';

// Debug endpoint to check what's happening
$method = $_SERVER['REQUEST_METHOD'];
$headers = getallheaders();
$input = file_get_contents('php://input');

$debug_info = [
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $method,
    'headers' => $headers,
    'raw_input' => $input,
    'get_params' => $_GET,
    'post_params' => $_POST,
    'server_info' => [
        'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? 'not set',
        'HTTP_HOST' => $_SERVER['HTTP_HOST'] ?? 'not set',
        'SERVER_NAME' => $_SERVER['SERVER_NAME'] ?? 'not set',
        'DOCUMENT_ROOT' => $_SERVER['DOCUMENT_ROOT'] ?? 'not set',
    ]
];

// Check if authorization header exists
$auth_token = null;
if (isset($headers['Authorization'])) {
    $authHeader = $headers['Authorization'];
    if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $auth_token = $matches[1];
        $debug_info['auth_token_found'] = true;
        $debug_info['auth_token_length'] = strlen($auth_token);
        $debug_info['auth_token_preview'] = substr($auth_token, 0, 20) . '...';
    } else {
        $debug_info['auth_header_format'] = 'Invalid format';
    }
} else {
    $debug_info['auth_token_found'] = false;
}

// Test database connection
try {
    $database = new Database();
    $conn = $database->getConnection();
    $debug_info['database_connection'] = 'success';
    
    // Test a simple query
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users");
    $stmt->execute();
    $userCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    $debug_info['user_count'] = $userCount;
    
} catch (Exception $e) {
    $debug_info['database_connection'] = 'failed';
    $debug_info['database_error'] = $e->getMessage();
}

// Test auth token if provided
if ($auth_token) {
    try {
        require_once __DIR__ . '/auth.php';
        
        $user = verifyToken($auth_token);
        if ($user) {
            $debug_info['token_verification'] = 'success';
            $debug_info['user_info'] = $user;
        } else {
            $debug_info['token_verification'] = 'failed';
            $debug_info['token_error'] = 'Token verification returned false';
        }
    } catch (Exception $e) {
        $debug_info['token_verification'] = 'error';
        $debug_info['token_error'] = $e->getMessage();
    }
}

jsonResponse([
    'success' => true,
    'message' => 'Debug information collected',
    'data' => $debug_info
]);
?>
