<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../includes/functions.php';

function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        throw new Exception('Database connection failed');
    }
    
    // Get notification counts
    $notifications = [];
    
    // New messages count
    $stmt = $conn->prepare("SELECT COUNT(*) FROM messages WHERE status = 'new'");
    $stmt->execute();
    $notifications['new_messages'] = (int)$stmt->fetchColumn();
    
    // Recent projects count (last 7 days)
    $stmt = $conn->prepare("SELECT COUNT(*) FROM projects WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
    $stmt->execute();
    $notifications['recent_projects'] = (int)$stmt->fetchColumn();
    
    // Ongoing projects count
    $stmt = $conn->prepare("SELECT COUNT(*) FROM projects WHERE status = 'ongoing'");
    $stmt->execute();
    $notifications['ongoing_projects'] = (int)$stmt->fetchColumn();
    
    // Recent media uploads (last 24 hours)
    $stmt = $conn->prepare("SELECT COUNT(*) FROM media WHERE uploaded_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $stmt->execute();
    $notifications['recent_media'] = (int)$stmt->fetchColumn();
    
    // System status
    $notifications['system_status'] = 'online';
    $notifications['last_updated'] = date('Y-m-d H:i:s');
    
    jsonResponse([
        'success' => true,
        'data' => $notifications
    ]);
    
} catch (PDOException $e) {
    error_log("Notifications API PDO error: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Database error'], 500);
} catch (Exception $e) {
    error_log("Notifications API error: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Server error'], 500);
}
?>
