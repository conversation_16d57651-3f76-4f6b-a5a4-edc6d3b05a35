import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Button } from 'react-native-paper';
import { theme } from '../theme/theme';

// For Expo, we'll create a simple date picker that works across platforms
export default function DatePicker({
  label,
  value,
  onDateChange,
  mode = 'date',
  minimumDate,
  maximumDate,
  placeholder = 'Select date',
  error,
  style,
}) {
  const [showPicker, setShowPicker] = useState(false);
  const [tempDate, setTempDate] = useState(value || new Date());

  const formatDate = (date) => {
    if (!date) return '';
    
    if (mode === 'date') {
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } else if (mode === 'time') {
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
      });
    } else {
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    }
  };

  const handleConfirm = () => {
    onDateChange(tempDate);
    setShowPicker(false);
  };

  const handleCancel = () => {
    setTempDate(value || new Date());
    setShowPicker(false);
  };

  const generateDateOptions = () => {
    const options = [];
    const currentYear = new Date().getFullYear();
    const startYear = minimumDate ? minimumDate.getFullYear() : currentYear - 5;
    const endYear = maximumDate ? maximumDate.getFullYear() : currentYear + 5;

    // Generate years
    for (let year = startYear; year <= endYear; year++) {
      options.push({ type: 'year', value: year });
    }

    return options;
  };

  const SimpleDatePicker = () => {
    const [selectedYear, setSelectedYear] = useState(tempDate.getFullYear());
    const [selectedMonth, setSelectedMonth] = useState(tempDate.getMonth());
    const [selectedDay, setSelectedDay] = useState(tempDate.getDate());

    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    const getDaysInMonth = (year, month) => {
      return new Date(year, month + 1, 0).getDate();
    };

    const updateTempDate = (year, month, day) => {
      const newDate = new Date(year, month, day);
      setTempDate(newDate);
    };

    const renderPicker = (items, selectedValue, onSelect, label) => (
      <View style={styles.pickerSection}>
        <Text style={styles.pickerLabel}>{label}</Text>
        <View style={styles.pickerContainer}>
          {items.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.pickerItem,
                selectedValue === item && styles.pickerItemSelected
              ]}
              onPress={() => {
                onSelect(item);
                if (label === 'Year') setSelectedYear(item);
                if (label === 'Month') setSelectedMonth(item);
                if (label === 'Day') setSelectedDay(item);
                updateTempDate(
                  label === 'Year' ? item : selectedYear,
                  label === 'Month' ? item : selectedMonth,
                  label === 'Day' ? item : selectedDay
                );
              }}
            >
              <Text style={[
                styles.pickerItemText,
                selectedValue === item && styles.pickerItemTextSelected
              ]}>
                {label === 'Month' ? months[item] : item}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );

    const years = Array.from({ length: 11 }, (_, i) => new Date().getFullYear() - 5 + i);
    const monthIndices = Array.from({ length: 12 }, (_, i) => i);
    const days = Array.from({ length: getDaysInMonth(selectedYear, selectedMonth) }, (_, i) => i + 1);

    return (
      <View style={styles.datePickerContainer}>
        {renderPicker(years, selectedYear, setSelectedYear, 'Year')}
        {renderPicker(monthIndices, selectedMonth, setSelectedMonth, 'Month')}
        {renderPicker(days, selectedDay, setSelectedDay, 'Day')}
      </View>
    );
  };

  return (
    <View style={[styles.container, style]}>
      {label && <Text style={styles.label}>{label}</Text>}
      
      <TouchableOpacity
        style={[styles.input, error && styles.inputError]}
        onPress={() => setShowPicker(true)}
      >
        <Text style={[styles.inputText, !value && styles.placeholder]}>
          {value ? formatDate(value) : placeholder}
        </Text>
        <Ionicons 
          name="calendar-outline" 
          size={20} 
          color={theme.colors.primary} 
        />
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error}</Text>}

      <Modal
        visible={showPicker}
        transparent={true}
        animationType="slide"
        onRequestClose={handleCancel}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                Select {mode === 'date' ? 'Date' : mode === 'time' ? 'Time' : 'Date & Time'}
              </Text>
            </View>

            <SimpleDatePicker />

            <View style={styles.modalActions}>
              <Button
                mode="outlined"
                onPress={handleCancel}
                style={styles.modalButton}
              >
                Cancel
              </Button>
              <Button
                mode="contained"
                onPress={handleConfirm}
                style={styles.modalButton}
              >
                Confirm
              </Button>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing.md,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  input: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: theme.colors.disabled,
    borderRadius: theme.roundness,
    padding: theme.spacing.md,
    backgroundColor: 'white',
  },
  inputError: {
    borderColor: theme.colors.error,
  },
  inputText: {
    fontSize: 16,
    color: theme.colors.text,
    flex: 1,
  },
  placeholder: {
    color: theme.colors.placeholder,
  },
  errorText: {
    fontSize: 14,
    color: theme.colors.error,
    marginTop: theme.spacing.xs,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: theme.roundness * 2,
    padding: theme.spacing.lg,
    width: '90%',
    maxWidth: 400,
    ...theme.shadows.large,
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  datePickerContainer: {
    marginBottom: theme.spacing.lg,
  },
  pickerSection: {
    marginBottom: theme.spacing.md,
  },
  pickerLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  pickerContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    maxHeight: 120,
  },
  pickerItem: {
    padding: theme.spacing.sm,
    margin: 2,
    borderRadius: theme.roundness,
    backgroundColor: '#f5f5f5',
    minWidth: 50,
    alignItems: 'center',
  },
  pickerItemSelected: {
    backgroundColor: theme.colors.primary,
  },
  pickerItemText: {
    fontSize: 14,
    color: theme.colors.text,
  },
  pickerItemTextSelected: {
    color: 'white',
    fontWeight: 'bold',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  modalButton: {
    flex: 1,
    marginHorizontal: theme.spacing.sm,
  },
});
