<?php
require_once __DIR__ . '/../includes/functions.php';
requireLogin();

$pageTitle = 'My Profile';

$message = '';
$messageType = '';

// Get current user data
$database = new Database();
$conn = $database->getConnection();

$stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$currentUser = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$currentUser) {
    header('Location: logout.php');
    exit();
}

// Handle profile update
if ($_POST && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action === 'update_profile') {
        $firstName = sanitizeInput($_POST['first_name'] ?? '');
        $lastName = sanitizeInput($_POST['last_name'] ?? '');
        $email = sanitizeInput($_POST['email'] ?? '');
        $phone = sanitizeInput($_POST['phone'] ?? '');
        $bio = sanitizeInput($_POST['bio'] ?? '');
        
        // Validate email
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $message = 'Please enter a valid email address.';
            $messageType = 'error';
        } else {
            // Check if email is already taken by another user
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $stmt->execute([$email, $_SESSION['user_id']]);
            
            if ($stmt->fetch()) {
                $message = 'This email address is already in use by another user.';
                $messageType = 'error';
            } else {
                try {
                    $stmt = $conn->prepare("UPDATE users SET first_name = ?, last_name = ?, email = ?, phone = ?, bio = ?, updated_at = NOW() WHERE id = ?");
                    $result = $stmt->execute([$firstName, $lastName, $email, $phone, $bio, $_SESSION['user_id']]);
                    
                    if ($result) {
                        $message = 'Profile updated successfully!';
                        $messageType = 'success';
                        
                        // Refresh user data
                        $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
                        $stmt->execute([$_SESSION['user_id']]);
                        $currentUser = $stmt->fetch(PDO::FETCH_ASSOC);
                    } else {
                        $message = 'Failed to update profile. Please try again.';
                        $messageType = 'error';
                    }
                } catch (PDOException $e) {
                    $message = 'Database error: ' . $e->getMessage();
                    $messageType = 'error';
                }
            }
        }
    }
    
    elseif ($action === 'change_password') {
        $currentPassword = $_POST['current_password'] ?? '';
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        // Validate current password
        if (!password_verify($currentPassword, $currentUser['password'])) {
            $message = 'Current password is incorrect.';
            $messageType = 'error';
        } elseif (strlen($newPassword) < 6) {
            $message = 'New password must be at least 6 characters long.';
            $messageType = 'error';
        } elseif ($newPassword !== $confirmPassword) {
            $message = 'New passwords do not match.';
            $messageType = 'error';
        } else {
            try {
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                $stmt = $conn->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
                $result = $stmt->execute([$hashedPassword, $_SESSION['user_id']]);
                
                if ($result) {
                    $message = 'Password changed successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to change password. Please try again.';
                    $messageType = 'error';
                }
            } catch (PDOException $e) {
                $message = 'Database error: ' . $e->getMessage();
                $messageType = 'error';
            }
        }
    }
    
    elseif ($action === 'upload_avatar') {
        if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
            $uploadResult = uploadFile($_FILES['avatar'], '../uploads/avatars/');
            
            if ($uploadResult['success']) {
                try {
                    // Delete old avatar if exists
                    if ($currentUser['avatar'] && file_exists('../' . $currentUser['avatar'])) {
                        unlink('../' . $currentUser['avatar']);
                    }
                    
                    $stmt = $conn->prepare("UPDATE users SET avatar = ?, updated_at = NOW() WHERE id = ?");
                    $result = $stmt->execute([$uploadResult['relative_path'], $_SESSION['user_id']]);
                    
                    if ($result) {
                        $message = 'Avatar updated successfully!';
                        $messageType = 'success';
                        
                        // Refresh user data
                        $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
                        $stmt->execute([$_SESSION['user_id']]);
                        $currentUser = $stmt->fetch(PDO::FETCH_ASSOC);
                    } else {
                        $message = 'Failed to update avatar. Please try again.';
                        $messageType = 'error';
                    }
                } catch (PDOException $e) {
                    $message = 'Database error: ' . $e->getMessage();
                    $messageType = 'error';
                }
            } else {
                $message = 'Avatar upload failed: ' . $uploadResult['message'];
                $messageType = 'error';
            }
        } else {
            $message = 'Please select a valid image file.';
            $messageType = 'error';
        }
    }
}

// Create avatars directory if it doesn't exist
$avatarDir = '../uploads/avatars/';
if (!file_exists($avatarDir)) {
    mkdir($avatarDir, 0755, true);
}

include 'includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-user-cog"></i> My Profile
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="dashboard.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType == 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show">
                    <i class="fas fa-<?php echo $messageType == 'error' ? 'exclamation-triangle' : 'check-circle'; ?>"></i>
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- Profile Information -->
                <div class="col-lg-8">
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user"></i> Profile Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="action" value="update_profile">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="first_name" class="form-label">First Name</label>
                                            <input type="text" class="form-control" id="first_name" name="first_name" 
                                                   value="<?php echo htmlspecialchars($currentUser['first_name'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="last_name" class="form-label">Last Name</label>
                                            <input type="text" class="form-control" id="last_name" name="last_name" 
                                                   value="<?php echo htmlspecialchars($currentUser['last_name'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="username" class="form-label">Username</label>
                                            <input type="text" class="form-control" id="username" 
                                                   value="<?php echo htmlspecialchars($currentUser['username']); ?>" disabled>
                                            <div class="form-text">Username cannot be changed</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">Email Address *</label>
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?php echo htmlspecialchars($currentUser['email']); ?>" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">Phone Number</label>
                                            <input type="tel" class="form-control" id="phone" name="phone" 
                                                   value="<?php echo htmlspecialchars($currentUser['phone'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="role" class="form-label">Role</label>
                                            <input type="text" class="form-control" id="role" 
                                                   value="<?php echo ucfirst($currentUser['role']); ?>" disabled>
                                            <div class="form-text">Role is managed by administrators</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="bio" class="form-label">Bio</label>
                                    <textarea class="form-control" id="bio" name="bio" rows="4" 
                                              placeholder="Tell us about yourself..."><?php echo htmlspecialchars($currentUser['bio'] ?? ''); ?></textarea>
                                </div>
                                
                                <div class="text-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Update Profile
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Change Password -->
                    <div class="card shadow">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-lock"></i> Change Password
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" id="passwordForm">
                                <input type="hidden" name="action" value="change_password">
                                
                                <div class="mb-3">
                                    <label for="current_password" class="form-label">Current Password *</label>
                                    <input type="password" class="form-control" id="current_password" name="current_password" required>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="new_password" class="form-label">New Password *</label>
                                            <input type="password" class="form-control" id="new_password" name="new_password" 
                                                   minlength="6" required>
                                            <div class="form-text">Minimum 6 characters</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="confirm_password" class="form-label">Confirm New Password *</label>
                                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                                   minlength="6" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="text-end">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-key"></i> Change Password
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Profile Picture & Stats -->
                <div class="col-lg-4">
                    <!-- Avatar -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-camera"></i> Profile Picture
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            <div class="profile-avatar mb-3">
                                <?php if ($currentUser['avatar'] && file_exists('../' . $currentUser['avatar'])): ?>
                                    <img src="../<?php echo htmlspecialchars($currentUser['avatar']); ?>" 
                                         alt="Profile Picture" class="avatar-large">
                                <?php else: ?>
                                    <div class="avatar-large avatar-placeholder">
                                        <?php echo strtoupper(substr($currentUser['username'], 0, 2)); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <form method="POST" enctype="multipart/form-data" id="avatarForm">
                                <input type="hidden" name="action" value="upload_avatar">
                                <div class="mb-3">
                                    <input type="file" class="form-control" id="avatar" name="avatar" 
                                           accept="image/jpeg,image/jpg,image/png,image/gif" onchange="previewAvatar(this)">
                                    <div class="form-text">JPG, PNG, GIF (max 2MB)</div>
                                </div>
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="fas fa-upload"></i> Upload New Picture
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Account Info -->
                    <div class="card shadow">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle"></i> Account Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="info-item">
                                <strong>Member Since:</strong><br>
                                <span class="text-muted"><?php echo date('F j, Y', strtotime($currentUser['created_at'])); ?></span>
                            </div>
                            
                            <div class="info-item">
                                <strong>Last Login:</strong><br>
                                <span class="text-muted">
                                    <?php if ($currentUser['last_login']): ?>
                                        <?php echo date('M j, Y g:i A', strtotime($currentUser['last_login'])); ?>
                                    <?php else: ?>
                                        Never
                                    <?php endif; ?>
                                </span>
                            </div>
                            
                            <div class="info-item">
                                <strong>Account Status:</strong><br>
                                <span class="badge bg-<?php echo $currentUser['status'] == 'active' ? 'success' : 'danger'; ?>">
                                    <?php echo ucfirst($currentUser['status'] ?? 'active'); ?>
                                </span>
                            </div>
                            
                            <div class="info-item">
                                <strong>Email Verified:</strong><br>
                                <span class="badge bg-<?php echo $currentUser['email_verified'] ? 'success' : 'warning'; ?>">
                                    <?php echo $currentUser['email_verified'] ? 'Verified' : 'Pending'; ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<style>
.profile-avatar {
    position: relative;
    display: inline-block;
}

.avatar-large {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.avatar-placeholder {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 2rem;
    border: 4px solid #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin: 0 auto;
}

.info-item {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.card {
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #e55a2b;
    border-color: #e55a2b;
}

.btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
}

.btn-warning:hover {
    background-color: #e0a800;
    border-color: #d39e00;
    color: #000;
}

.password-strength {
    height: 4px;
    border-radius: 2px;
    margin-top: 5px;
    transition: all 0.3s ease;
}

.strength-weak { background-color: #dc3545; }
.strength-medium { background-color: #ffc107; }
.strength-strong { background-color: #28a745; }

@media (max-width: 768px) {
    .avatar-large,
    .avatar-placeholder {
        width: 100px;
        height: 100px;
        font-size: 1.5rem;
    }
}
</style>

<script>
// Password confirmation validation
document.getElementById('passwordForm').addEventListener('submit', function(e) {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;

    if (newPassword !== confirmPassword) {
        e.preventDefault();
        alert('New passwords do not match!');
        return false;
    }

    if (newPassword.length < 6) {
        e.preventDefault();
        alert('Password must be at least 6 characters long!');
        return false;
    }
});

// Password strength indicator
document.getElementById('new_password').addEventListener('input', function() {
    const password = this.value;
    const strengthBar = document.getElementById('password-strength');

    if (!strengthBar) {
        // Create strength indicator
        const indicator = document.createElement('div');
        indicator.id = 'password-strength';
        indicator.className = 'password-strength';
        this.parentNode.appendChild(indicator);
    }

    const strength = calculatePasswordStrength(password);
    const bar = document.getElementById('password-strength');

    bar.className = 'password-strength';
    if (strength < 3) {
        bar.classList.add('strength-weak');
        bar.style.width = '33%';
    } else if (strength < 5) {
        bar.classList.add('strength-medium');
        bar.style.width = '66%';
    } else {
        bar.classList.add('strength-strong');
        bar.style.width = '100%';
    }
});

function calculatePasswordStrength(password) {
    let strength = 0;

    if (password.length >= 6) strength++;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;

    return strength;
}

// Avatar preview
function previewAvatar(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Validate file type
        if (!file.type.match('image.*')) {
            alert('Please select a valid image file.');
            input.value = '';
            return;
        }

        // Validate file size (2MB)
        if (file.size > 2 * 1024 * 1024) {
            alert('File size must be less than 2MB.');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            const avatar = document.querySelector('.avatar-large, .avatar-placeholder');
            if (avatar) {
                if (avatar.tagName === 'IMG') {
                    avatar.src = e.target.result;
                } else {
                    // Replace placeholder with image
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'avatar-large';
                    img.alt = 'Profile Picture Preview';
                    avatar.parentNode.replaceChild(img, avatar);
                }
            }
        };
        reader.readAsDataURL(file);
    }
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    // Email validation
    const emailInput = document.getElementById('email');
    emailInput.addEventListener('blur', function() {
        const email = this.value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (email && !emailRegex.test(email)) {
            this.classList.add('is-invalid');
            if (!this.nextElementSibling || !this.nextElementSibling.classList.contains('invalid-feedback')) {
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = 'Please enter a valid email address.';
                this.parentNode.appendChild(feedback);
            }
        } else {
            this.classList.remove('is-invalid');
            const feedback = this.parentNode.querySelector('.invalid-feedback');
            if (feedback) {
                feedback.remove();
            }
        }
    });

    // Phone number formatting
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length >= 6) {
            value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
        } else if (value.length >= 3) {
            value = value.replace(/(\d{3})(\d{3})/, '($1) $2');
        }
        this.value = value;
    });
});

// Auto-save draft functionality
let autoSaveTimer;
const formInputs = document.querySelectorAll('#first_name, #last_name, #email, #phone, #bio');

formInputs.forEach(input => {
    input.addEventListener('input', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(saveDraft, 2000); // Save after 2 seconds of inactivity
    });
});

function saveDraft() {
    const formData = {
        first_name: document.getElementById('first_name').value,
        last_name: document.getElementById('last_name').value,
        email: document.getElementById('email').value,
        phone: document.getElementById('phone').value,
        bio: document.getElementById('bio').value
    };

    localStorage.setItem('profile_draft', JSON.stringify(formData));

    // Show subtle indication that draft was saved
    const indicator = document.createElement('div');
    indicator.className = 'alert alert-info alert-dismissible fade show position-fixed';
    indicator.style.cssText = 'top: 20px; right: 20px; z-index: 9999; opacity: 0.8;';
    indicator.innerHTML = '<small><i class="fas fa-save"></i> Draft saved</small>';
    document.body.appendChild(indicator);

    setTimeout(() => {
        indicator.remove();
    }, 2000);
}

// Load draft on page load
window.addEventListener('load', function() {
    const draft = localStorage.getItem('profile_draft');
    if (draft) {
        const data = JSON.parse(draft);
        Object.keys(data).forEach(key => {
            const input = document.getElementById(key);
            if (input && !input.value) {
                input.value = data[key];
            }
        });
    }
});

// Clear draft when form is successfully submitted
if (window.location.search.includes('success')) {
    localStorage.removeItem('profile_draft');
}
</script>

<?php include 'includes/admin_footer.php'; ?>
