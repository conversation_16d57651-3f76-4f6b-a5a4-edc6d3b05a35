<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../includes/functions.php';

function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

try {
    // Get form data
    $name = sanitizeInput($_POST['name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $subject = sanitizeInput($_POST['subject'] ?? '');
    $messageText = sanitizeInput($_POST['message'] ?? '');
    
    // Validate required fields
    if (empty($name)) {
        jsonResponse(['success' => false, 'message' => 'Name is required'], 400);
    }
    
    if (empty($email)) {
        jsonResponse(['success' => false, 'message' => 'Email is required'], 400);
    }
    
    if (empty($messageText)) {
        jsonResponse(['success' => false, 'message' => 'Message is required'], 400);
    }
    
    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        jsonResponse(['success' => false, 'message' => 'Please enter a valid email address'], 400);
    }
    
    // Validate message length
    if (strlen($messageText) < 10) {
        jsonResponse(['success' => false, 'message' => 'Message must be at least 10 characters long'], 400);
    }
    
    if (strlen($messageText) > 5000) {
        jsonResponse(['success' => false, 'message' => 'Message is too long (maximum 5000 characters)'], 400);
    }
    
    // Basic spam protection
    $spamKeywords = ['viagra', 'casino', 'lottery', 'winner', 'congratulations', 'click here', 'free money'];
    $messageTextLower = strtolower($messageText);
    foreach ($spamKeywords as $keyword) {
        if (strpos($messageTextLower, $keyword) !== false) {
            jsonResponse(['success' => false, 'message' => 'Message contains prohibited content'], 400);
        }
    }
    
    // Rate limiting - check if same email sent message in last 5 minutes
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        throw new Exception('Database connection failed');
    }
    
    $stmt = $conn->prepare("SELECT COUNT(*) FROM messages WHERE email = ? AND created_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)");
    $stmt->execute([$email]);
    $recentCount = $stmt->fetchColumn();
    
    if ($recentCount > 0) {
        jsonResponse(['success' => false, 'message' => 'Please wait a few minutes before sending another message'], 429);
    }
    
    // Insert message into database
    $stmt = $conn->prepare("INSERT INTO messages (name, email, phone, subject, message, status) VALUES (?, ?, ?, ?, ?, 'new')");
    $result = $stmt->execute([$name, $email, $phone, $subject, $messageText]);
    
    if (!$result) {
        throw new Exception('Failed to save message to database');
    }
    
    $messageId = $conn->lastInsertId();
    
    // Optional: Send email notification to admin
    try {
        sendEmailNotification($name, $email, $subject, $messageText);
    } catch (Exception $e) {
        // Log email error but don't fail the request
        error_log("Email notification failed: " . $e->getMessage());
    }
    
    // Success response
    jsonResponse([
        'success' => true,
        'message' => 'Thank you for your message! We will get back to you soon.',
        'data' => [
            'message_id' => $messageId,
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ]);
    
} catch (PDOException $e) {
    error_log("Contact API PDO error: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Database error occurred. Please try again later.'], 500);
} catch (Exception $e) {
    error_log("Contact API error: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'An error occurred. Please try again later.'], 500);
}

function sendEmailNotification($name, $email, $subject, $message) {
    // Get admin email from settings
    $adminEmail = getSetting('company_email');
    if (!$adminEmail) {
        $adminEmail = '<EMAIL>'; // fallback
    }
    
    $emailSubject = "New Contact Form Submission: " . ($subject ?: 'General Inquiry');
    $emailBody = "
New message received from the website contact form:

Name: $name
Email: $email
Subject: " . ($subject ?: 'General Inquiry') . "

Message:
$message

---
This message was sent from the Flori Construction website contact form.
Reply directly to this email to respond to the customer.
    ";
    
    $headers = "From: $email\r\n";
    $headers .= "Reply-To: $email\r\n";
    $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion();
    
    // Use mail() function (in production, consider using PHPMailer or similar)
    $mailSent = mail($adminEmail, $emailSubject, $emailBody, $headers);
    
    if (!$mailSent) {
        throw new Exception('Failed to send email notification');
    }
    
    return true;
}
?>
