import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import { apiService } from '../services/apiService';
import { theme } from '../theme/theme';

export default function ProfileScreen({ navigation }) {
  const { user, logout } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    username: user?.username || '',
    email: user?.email || '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const handleSave = () => {
    if (formData.newPassword && formData.newPassword !== formData.confirmPassword) {
      Alert.alert('Error', 'New passwords do not match');
      return;
    }

    Alert.alert(
      'Save Changes',
      'Are you sure you want to save these changes?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Save', onPress: confirmSave },
      ]
    );
  };

  const confirmSave = async () => {
    try {
      console.log('💾 Updating profile...');

      const updateData = {
        username: formData.username,
        email: formData.email,
      };

      // Only include password if user wants to change it
      if (formData.newPassword) {
        updateData.current_password = formData.currentPassword;
        updateData.new_password = formData.newPassword;
      }

      const response = await apiService.updateProfile(updateData);

      if (response.success) {
        Alert.alert('Success', 'Profile updated successfully');
        setIsEditing(false);
        setFormData(prev => ({
          ...prev,
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        }));
      } else {
        Alert.alert('Error', response.message || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: logout },
      ]
    );
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const ProfileItem = ({ icon, label, value, editable = false, secure = false, field }) => (
    <View style={styles.profileItem}>
      <View style={styles.profileItemLeft}>
        <Ionicons name={icon} size={20} color={theme.colors.primary} />
        <Text style={styles.profileLabel}>{label}</Text>
      </View>
      {isEditing && editable ? (
        <TextInput
          style={styles.profileInput}
          value={formData[field]}
          onChangeText={(value) => handleInputChange(field, value)}
          secureTextEntry={secure}
          placeholder={`Enter ${label.toLowerCase()}`}
          placeholderTextColor={theme.colors.placeholder}
        />
      ) : (
        <Text style={styles.profileValue}>{value || 'Not set'}</Text>
      )}
    </View>
  );

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.avatarContainer}>
          <Ionicons name="person" size={40} color="white" />
        </View>
        <Text style={styles.userName}>{user?.username || 'Admin User'}</Text>
        <Text style={styles.userRole}>{user?.role || 'Administrator'}</Text>
      </View>

      {/* Profile Information */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Profile Information</Text>
          <TouchableOpacity
            style={styles.editButton}
            onPress={() => setIsEditing(!isEditing)}
          >
            <Ionicons
              name={isEditing ? "close" : "pencil"}
              size={20}
              color={theme.colors.primary}
            />
          </TouchableOpacity>
        </View>

        <ProfileItem
          icon="person-outline"
          label="Username"
          value={formData.username}
          editable={true}
          field="username"
        />

        <ProfileItem
          icon="mail-outline"
          label="Email"
          value={formData.email}
          editable={true}
          field="email"
        />

        <ProfileItem
          icon="shield-outline"
          label="Role"
          value={user?.role || 'Administrator'}
          editable={false}
        />

        <ProfileItem
          icon="calendar-outline"
          label="Member Since"
          value={user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'Unknown'}
          editable={false}
        />
      </View>

      {/* Change Password */}
      {isEditing && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Change Password</Text>

          <ProfileItem
            icon="lock-closed-outline"
            label="Current Password"
            value={formData.currentPassword}
            editable={true}
            secure={true}
            field="currentPassword"
          />

          <ProfileItem
            icon="key-outline"
            label="New Password"
            value={formData.newPassword}
            editable={true}
            secure={true}
            field="newPassword"
          />

          <ProfileItem
            icon="key-outline"
            label="Confirm Password"
            value={formData.confirmPassword}
            editable={true}
            secure={true}
            field="confirmPassword"
          />
        </View>
      )}

      {/* Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Actions</Text>

        {isEditing && (
          <TouchableOpacity style={styles.actionButton} onPress={handleSave}>
            <Ionicons name="save-outline" size={20} color={theme.colors.success} />
            <Text style={[styles.actionText, { color: theme.colors.success }]}>
              Save Changes
            </Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => navigation.navigate('Settings')}
        >
          <Ionicons name="settings-outline" size={20} color={theme.colors.primary} />
          <Text style={styles.actionText}>App Settings</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={handleLogout}>
          <Ionicons name="log-out-outline" size={20} color={theme.colors.error} />
          <Text style={[styles.actionText, { color: theme.colors.error }]}>
            Logout
          </Text>
        </TouchableOpacity>
      </View>

      {/* App Information */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>App Information</Text>

        <View style={styles.appInfo}>
          <Text style={styles.appInfoText}>Flori Construction Admin</Text>
          <Text style={styles.appInfoText}>Version 1.0.0</Text>
          <Text style={styles.appInfoText}>© 2024 Flori Construction Ltd</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    backgroundColor: theme.colors.primary,
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: theme.spacing.xs,
  },
  userRole: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  section: {
    backgroundColor: 'white',
    margin: theme.spacing.md,
    padding: theme.spacing.md,
    borderRadius: theme.roundness,
    ...theme.shadows.small,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  editButton: {
    padding: theme.spacing.sm,
  },
  profileItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  profileItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  profileLabel: {
    fontSize: 16,
    color: theme.colors.text,
    marginLeft: theme.spacing.md,
    fontWeight: '500',
  },
  profileValue: {
    fontSize: 16,
    color: theme.colors.placeholder,
    flex: 1,
    textAlign: 'right',
  },
  profileInput: {
    fontSize: 16,
    color: theme.colors.text,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: theme.roundness,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  actionText: {
    fontSize: 16,
    color: theme.colors.text,
    marginLeft: theme.spacing.md,
    fontWeight: '500',
  },
  appInfo: {
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
  },
  appInfoText: {
    fontSize: 14,
    color: theme.colors.placeholder,
    marginBottom: 4,
  },
});
