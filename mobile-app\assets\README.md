# Assets Directory

This directory contains app assets for the Flori Construction Admin mobile app.

## Current Status

The app is currently configured to run **WITHOUT** requiring any asset files. This allows for immediate testing and development.

## Optional Assets (For Production)

If you want to add custom branding later, you can create these files:

### App Icons

- `icon.png` - App icon (1024x1024px)
- `adaptive-icon.png` - Android adaptive icon (1024x1024px)
- `favicon.png` - Web favicon (48x48px)

### Splash Screen

- `splash.png` - Splash screen image (1284x2778px for iPhone 13 Pro Max)

## Adding Assets Later

To add custom assets:

1. Create the image files in this directory
2. Update `app.json` to reference them:

   ```json
   {
     "expo": {
       "icon": "./assets/icon.png",
       "splash": {
         "image": "./assets/splash.png"
       },
       "android": {
         "adaptiveIcon": {
           "foregroundImage": "./assets/adaptive-icon.png"
         }
       }
     }
   }
   ```

## Asset Creation Tools

1. **App Icon Generator**: <https://www.appicon.co/>
2. **Expo Asset Generator**: <https://docs.expo.dev/guides/app-icons/>
3. **Canva**: <https://www.canva.com/> (for custom designs)
4. **Figma**: <https://www.figma.com/> (for professional designs)

## Current Configuration

The app uses:

- **Default Expo icon** (construction/business theme)
- **Solid color splash** (#ff6b35 - brand orange)
- **No custom assets required** for development

This allows the app to run immediately without any asset dependencies!
