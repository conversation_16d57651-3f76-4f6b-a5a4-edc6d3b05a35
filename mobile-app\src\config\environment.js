import Constants from 'expo-constants';
import { Platform } from 'react-native';

// Environment configuration
const ENV = {
  development: {
    apiUrl: __DEV__ ? getLocalApiUrl() : 'http://***********/mobile-web-app/api',
    timeout: 15000,
    retryAttempts: 3,
    retryDelay: 1000,
  },
  staging: {
    apiUrl: 'https://staging.yourdomain.com/api',
    timeout: 10000,
    retryAttempts: 2,
    retryDelay: 1500,
  },
  production: {
    apiUrl: 'https://yourdomain.com/api',
    timeout: 8000,
    retryAttempts: 2,
    retryDelay: 2000,
  }
};

// Auto-detect local IP for development
function getLocalApiUrl() {
  // Try to get the local IP from Expo's manifest
  const manifest = Constants.manifest;
  
  if (manifest?.debuggerHost) {
    const localIp = manifest.debuggerHost.split(':')[0];
    return `http://${localIp}/mobile-web-app/api`;
  }
  
  // Fallback IPs for common development scenarios
  const fallbackIps = [
    '***********',  // Common home network
    '***********',  // Common home network
    '**********',     // Common office network
    '************',   // Docker/VM network
    'localhost'       // Local development
  ];
  
  // For now, return the first fallback
  // In a real app, you might want to test connectivity to each
  return `http://${fallbackIps[0]}/mobile-web-app/api`;
}

// Get current environment
function getCurrentEnvironment() {
  // Check if we're in development mode
  if (__DEV__) {
    return 'development';
  }
  
  // Check for staging environment (you can set this via app.json or environment variables)
  if (Constants.manifest?.extra?.environment === 'staging') {
    return 'staging';
  }
  
  // Default to production
  return 'production';
}

// Get configuration for current environment
function getConfig() {
  const currentEnv = getCurrentEnvironment();
  const config = ENV[currentEnv];
  
  return {
    ...config,
    environment: currentEnv,
    isProduction: currentEnv === 'production',
    isDevelopment: currentEnv === 'development',
    platform: Platform.OS,
  };
}

// Export configuration
export const config = getConfig();

// Export individual values for convenience
export const {
  apiUrl,
  timeout,
  retryAttempts,
  retryDelay,
  environment,
  isProduction,
  isDevelopment,
} = config;

// Helper function to build full API URLs
export const buildApiUrl = (endpoint) => {
  const baseUrl = apiUrl.endsWith('/') ? apiUrl.slice(0, -1) : apiUrl;
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  return `${baseUrl}${cleanEndpoint}`;
};

// Network configuration
export const networkConfig = {
  // Connection timeout
  timeout,
  
  // Retry configuration
  retry: {
    attempts: retryAttempts,
    delay: retryDelay,
    // Exponential backoff multiplier
    backoffMultiplier: 1.5,
    // Maximum delay between retries
    maxDelay: 10000,
  },
  
  // Request headers
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-App-Platform': Platform.OS,
    'X-App-Version': Constants.manifest?.version || '1.0.0',
  },
};

// Debug logging
if (isDevelopment) {
  console.log('🔧 Environment Configuration:', {
    environment,
    apiUrl,
    platform: Platform.OS,
    timeout,
    retryAttempts,
  });
}
