import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as SecureStore from 'expo-secure-store';
import { theme } from '../theme/theme';

export default function SettingsScreen({ navigation }) {
  const [settings, setSettings] = useState({
    notifications: true,
    pushNotifications: true,
    emailNotifications: false,
    darkMode: false,
    autoSync: true,
    offlineMode: false,
    debugMode: false,
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = await SecureStore.getItemAsync('appSettings');
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
        console.log('✅ Settings loaded successfully');
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      // Use default settings if loading fails
    }
  };

  const saveSettings = async (newSettings) => {
    try {
      await SecureStore.setItemAsync('appSettings', JSON.stringify(newSettings));
      setSettings(newSettings);
      console.log('✅ Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      Alert.alert('Error', 'Failed to save settings');
    }
  };

  const toggleSetting = (key) => {
    const newSettings = { ...settings, [key]: !settings[key] };
    saveSettings(newSettings);
  };

  const clearCache = () => {
    Alert.alert(
      'Clear Cache',
      'This will clear all cached data. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Clear', style: 'destructive', onPress: confirmClearCache },
      ]
    );
  };

  const confirmClearCache = async () => {
    try {
      // For SecureStore, we'll just show a success message since it doesn't have getAllKeys
      // In a real app, you'd clear specific cache data you know about
      Alert.alert('Success', 'Cache cleared successfully');
    } catch (error) {
      console.error('Error clearing cache:', error);
      Alert.alert('Error', 'Failed to clear cache');
    }
  };

  const resetSettings = () => {
    Alert.alert(
      'Reset Settings',
      'This will reset all settings to default values. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Reset', style: 'destructive', onPress: confirmResetSettings },
      ]
    );
  };

  const confirmResetSettings = () => {
    const defaultSettings = {
      notifications: true,
      pushNotifications: true,
      emailNotifications: false,
      darkMode: false,
      autoSync: true,
      offlineMode: false,
      debugMode: false,
    };
    saveSettings(defaultSettings);
    Alert.alert('Success', 'Settings reset to default values');
  };

  const SettingItem = ({ icon, title, description, value, onToggle, type = 'switch' }) => (
    <View style={styles.settingItem}>
      <View style={styles.settingLeft}>
        <Ionicons name={icon} size={24} color={theme.colors.primary} />
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>{title}</Text>
          {description && (
            <Text style={styles.settingDescription}>{description}</Text>
          )}
        </View>
      </View>
      {type === 'switch' && (
        <Switch
          value={value}
          onValueChange={onToggle}
          trackColor={{ false: '#767577', true: theme.colors.primary }}
          thumbColor={value ? '#fff' : '#f4f3f4'}
        />
      )}
    </View>
  );

  const ActionItem = ({ icon, title, description, onPress, color = theme.colors.text }) => (
    <TouchableOpacity style={styles.actionItem} onPress={onPress}>
      <View style={styles.settingLeft}>
        <Ionicons name={icon} size={24} color={color} />
        <View style={styles.settingText}>
          <Text style={[styles.settingTitle, { color }]}>{title}</Text>
          {description && (
            <Text style={styles.settingDescription}>{description}</Text>
          )}
        </View>
      </View>
      <Ionicons name="chevron-forward" size={20} color={theme.colors.placeholder} />
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container}>
      {/* Notifications Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Notifications</Text>

        <SettingItem
          icon="notifications-outline"
          title="Enable Notifications"
          description="Receive app notifications"
          value={settings.notifications}
          onToggle={() => toggleSetting('notifications')}
        />

        <SettingItem
          icon="phone-portrait-outline"
          title="Push Notifications"
          description="Receive push notifications on your device"
          value={settings.pushNotifications}
          onToggle={() => toggleSetting('pushNotifications')}
        />

        <SettingItem
          icon="mail-outline"
          title="Email Notifications"
          description="Receive notifications via email"
          value={settings.emailNotifications}
          onToggle={() => toggleSetting('emailNotifications')}
        />
      </View>

      {/* App Preferences Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>App Preferences</Text>

        <SettingItem
          icon="moon-outline"
          title="Dark Mode"
          description="Use dark theme (Coming Soon)"
          value={settings.darkMode}
          onToggle={() => toggleSetting('darkMode')}
        />

        <SettingItem
          icon="sync-outline"
          title="Auto Sync"
          description="Automatically sync data when online"
          value={settings.autoSync}
          onToggle={() => toggleSetting('autoSync')}
        />

        <SettingItem
          icon="cloud-offline-outline"
          title="Offline Mode"
          description="Enable offline functionality"
          value={settings.offlineMode}
          onToggle={() => toggleSetting('offlineMode')}
        />
      </View>

      {/* Developer Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Developer</Text>

        <SettingItem
          icon="bug-outline"
          title="Debug Mode"
          description="Show debug information and logs"
          value={settings.debugMode}
          onToggle={() => toggleSetting('debugMode')}
        />
      </View>

      {/* Actions Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Actions</Text>

        <ActionItem
          icon="trash-outline"
          title="Clear Cache"
          description="Clear app cache and temporary data"
          onPress={clearCache}
        />

        <ActionItem
          icon="refresh-outline"
          title="Reset Settings"
          description="Reset all settings to default values"
          onPress={resetSettings}
          color={theme.colors.warning}
        />
      </View>

      {/* App Info Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>About</Text>

        <View style={styles.appInfo}>
          <Text style={styles.appInfoText}>Flori Construction Admin</Text>
          <Text style={styles.appInfoText}>Version 1.0.0</Text>
          <Text style={styles.appInfoText}>Build 2024.06.04</Text>
          <Text style={styles.appInfoText}>© 2024 Flori Construction Ltd</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  section: {
    backgroundColor: 'white',
    margin: theme.spacing.md,
    borderRadius: theme.roundness,
    ...theme.shadows.small,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    padding: theme.spacing.md,
    paddingBottom: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#f8f9fa',
  },
  actionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#f8f9fa',
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingText: {
    marginLeft: theme.spacing.md,
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    color: theme.colors.placeholder,
    lineHeight: 18,
  },
  appInfo: {
    padding: theme.spacing.md,
    alignItems: 'center',
  },
  appInfoText: {
    fontSize: 14,
    color: theme.colors.placeholder,
    marginBottom: 4,
    textAlign: 'center',
  },
});
