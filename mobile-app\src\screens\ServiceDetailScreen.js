import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { apiService } from '../services/apiService';
import { theme } from '../theme/theme';

export default function ServiceDetailScreen({ route, navigation }) {
  const { service } = route.params;

  const getIconName = (iconClass) => {
    const iconMap = {
      'fas fa-tools': 'construct',
      'fas fa-hard-hat': 'shield',
      'fas fa-hammer': 'hammer',
      'fas fa-wrench': 'build',
      'fas fa-cogs': 'settings',
      'fas fa-building': 'business',
      'fas fa-home': 'home',
      'fas fa-industry': 'business',
      'fas fa-truck': 'car',
      'fas fa-tractor': 'car-sport',
      'fas fa-paint-roller': 'brush',
      'fas fa-ruler-combined': 'resize',
      'fas fa-drafting-compass': 'compass',
      'fas fa-layer-group': 'layers',
    };
    return iconMap[iconClass] || 'construct';
  };

  const handleEdit = () => {
    navigation.navigate('AddService', { service, isEdit: true });
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Service',
      'Are you sure you want to delete this service?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: confirmDelete },
      ]
    );
  };

  const confirmDelete = async () => {
    try {
      console.log('🗑️ Deleting service:', service.id);
      const response = await apiService.deleteService(service.id);

      if (response.success) {
        Alert.alert('Success', 'Service deleted successfully', [
          { text: 'OK', onPress: () => navigation.goBack() }
        ]);
      } else {
        Alert.alert('Error', response.message || 'Failed to delete service');
      }
    } catch (error) {
      console.error('Error deleting service:', error);
      Alert.alert('Error', 'Failed to delete service. Please try again.');
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <Ionicons
              name={getIconName(service.icon)}
              size={48}
              color={theme.colors.primary}
            />
          </View>
          <View style={styles.headerInfo}>
            <Text style={styles.title}>{service.title}</Text>
            <View style={[
              styles.statusBadge,
              { backgroundColor: service.status === 'active' ? theme.colors.success : theme.colors.placeholder }
            ]}>
              <Text style={styles.statusText}>{service.status}</Text>
            </View>
          </View>
        </View>

        {/* Description */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Service Description</Text>
          <Text style={styles.description}>{service.description}</Text>
        </View>

        {/* Service Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Service Information</Text>

          <View style={styles.infoRow}>
            <Ionicons name="calendar-outline" size={20} color={theme.colors.primary} />
            <Text style={styles.infoText}>
              Created: {new Date(service.created_at).toLocaleDateString()}
            </Text>
          </View>

          {service.updated_at && (
            <View style={styles.infoRow}>
              <Ionicons name="refresh-outline" size={20} color={theme.colors.primary} />
              <Text style={styles.infoText}>
                Updated: {new Date(service.updated_at).toLocaleDateString()}
              </Text>
            </View>
          )}

          <View style={styles.infoRow}>
            <Ionicons name="checkmark-circle-outline" size={20} color={theme.colors.primary} />
            <Text style={styles.infoText}>
              Status: {service.status === 'active' ? 'Active' : 'Inactive'}
            </Text>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.editButton} onPress={handleEdit}>
            <Ionicons name="pencil" size={20} color="white" />
            <Text style={styles.buttonText}>Edit Service</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.deleteButton} onPress={handleDelete}>
            <Ionicons name="trash" size={20} color="white" />
            <Text style={styles.buttonText}>Delete</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    padding: theme.spacing.md,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: theme.spacing.lg,
    borderRadius: theme.roundness,
    marginBottom: theme.spacing.lg,
    ...theme.shadows.small,
  },
  iconContainer: {
    width: 80,
    height: 80,
    backgroundColor: `${theme.colors.primary}20`,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.lg,
  },
  headerInfo: {
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: 20,
  },
  statusText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
    textTransform: 'capitalize',
  },
  section: {
    backgroundColor: 'white',
    padding: theme.spacing.md,
    borderRadius: theme.roundness,
    marginBottom: theme.spacing.lg,
    ...theme.shadows.small,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  description: {
    fontSize: 16,
    color: theme.colors.text,
    lineHeight: 24,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  infoText: {
    fontSize: 16,
    color: theme.colors.text,
    marginLeft: theme.spacing.md,
    flex: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: theme.spacing.lg,
  },
  editButton: {
    backgroundColor: theme.colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.roundness,
    flex: 1,
    marginRight: theme.spacing.sm,
    justifyContent: 'center',
  },
  deleteButton: {
    backgroundColor: theme.colors.error,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.roundness,
    flex: 1,
    marginLeft: theme.spacing.sm,
    justifyContent: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: theme.spacing.sm,
  },
});
