<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../includes/functions.php';

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

switch ($method) {
    case 'POST':
        if (isset($_GET['action'])) {
            switch ($_GET['action']) {
                case 'login':
                    handleLogin($input);
                    break;
                case 'logout':
                    handleLogout();
                    break;
                case 'refresh':
                    handleRefreshToken();
                    break;
                default:
                    jsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
            }
        } else {
            jsonResponse(['success' => false, 'message' => 'Action required'], 400);
        }
        break;

    case 'PUT':
        if (isset($_GET['action']) && $_GET['action'] === 'update_profile') {
            handleUpdateProfile($input);
        } else {
            jsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
        }
        break;

    case 'GET':
        if (isset($_GET['action']) && $_GET['action'] === 'verify') {
            handleVerifyToken();
        } else {
            jsonResponse(['success' => false, 'message' => 'Invalid request'], 400);
        }
        break;

    default:
        jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

function handleLogin($input) {
    if (!isset($input['username']) || !isset($input['password'])) {
        jsonResponse(['success' => false, 'message' => 'Username and password required'], 400);
    }
    
    $username = sanitizeInput($input['username']);
    $password = $input['password'];
    
    $database = new Database();
    $conn = $database->getConnection();
    
    $stmt = $conn->prepare("SELECT id, username, email, password, role FROM users WHERE username = ? OR email = ?");
    $stmt->execute([$username, $username]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user && password_verify($password, $user['password'])) {
        // Generate JWT token (simplified version)
        $token = generateToken($user);
        
        jsonResponse([
            'success' => true,
            'message' => 'Login successful',
            'data' => [
                'token' => $token,
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'role' => $user['role']
                ]
            ]
        ]);
    } else {
        jsonResponse(['success' => false, 'message' => 'Invalid credentials'], 401);
    }
}

function handleLogout() {
    // In a real implementation, you would invalidate the token
    jsonResponse(['success' => true, 'message' => 'Logged out successfully']);
}

function handleVerifyToken() {
    $headers = getallheaders();
    $token = null;
    
    if (isset($headers['Authorization'])) {
        $authHeader = $headers['Authorization'];
        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            $token = $matches[1];
        }
    }
    
    if (!$token) {
        jsonResponse(['success' => false, 'message' => 'Token required'], 401);
    }
    
    $user = verifyToken($token);
    if ($user) {
        jsonResponse([
            'success' => true,
            'message' => 'Token valid',
            'data' => ['user' => $user]
        ]);
    } else {
        jsonResponse(['success' => false, 'message' => 'Invalid token'], 401);
    }
}

function handleRefreshToken() {
    $headers = getallheaders();
    $token = null;
    
    if (isset($headers['Authorization'])) {
        $authHeader = $headers['Authorization'];
        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            $token = $matches[1];
        }
    }
    
    if (!$token) {
        jsonResponse(['success' => false, 'message' => 'Token required'], 401);
    }
    
    $user = verifyToken($token);
    if ($user) {
        $newToken = generateToken($user);
        jsonResponse([
            'success' => true,
            'message' => 'Token refreshed',
            'data' => ['token' => $newToken]
        ]);
    } else {
        jsonResponse(['success' => false, 'message' => 'Invalid token'], 401);
    }
}

function generateToken($user) {
    // Simplified JWT-like token generation
    // In production, use a proper JWT library
    $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
    $payload = json_encode([
        'user_id' => $user['id'],
        'username' => $user['username'],
        'role' => $user['role'],
        'exp' => time() + (24 * 60 * 60) // 24 hours
    ]);
    
    $headerEncoded = base64url_encode($header);
    $payloadEncoded = base64url_encode($payload);
    
    $signature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, 'your-secret-key', true);
    $signatureEncoded = base64url_encode($signature);
    
    return $headerEncoded . "." . $payloadEncoded . "." . $signatureEncoded;
}

function verifyToken($token) {
    $parts = explode('.', $token);
    if (count($parts) !== 3) {
        return false;
    }
    
    list($headerEncoded, $payloadEncoded, $signatureEncoded) = $parts;
    
    $signature = base64url_decode($signatureEncoded);
    $expectedSignature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, 'your-secret-key', true);
    
    if (!hash_equals($signature, $expectedSignature)) {
        return false;
    }
    
    $payload = json_decode(base64url_decode($payloadEncoded), true);
    
    if ($payload['exp'] < time()) {
        return false; // Token expired
    }
    
    return [
        'id' => $payload['user_id'],
        'username' => $payload['username'],
        'role' => $payload['role']
    ];
}

function base64url_encode($data) {
    return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
}

function base64url_decode($data) {
    return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
}

function handleUpdateProfile($input) {
    $user = requireAuth();

    if (!$input) {
        jsonResponse(['success' => false, 'message' => 'No data provided'], 400);
    }

    $database = new Database();
    $conn = $database->getConnection();

    try {
        $updateFields = [];
        $params = [];

        // Handle username update
        if (isset($input['username']) && !empty($input['username'])) {
            $username = sanitizeInput($input['username']);

            // Check if username is already taken by another user
            $stmt = $conn->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
            $stmt->execute([$username, $user['id']]);
            if ($stmt->fetch()) {
                jsonResponse(['success' => false, 'message' => 'Username already taken'], 400);
            }

            $updateFields[] = "username = ?";
            $params[] = $username;
        }

        // Handle email update
        if (isset($input['email']) && !empty($input['email'])) {
            $email = sanitizeInput($input['email']);

            // Validate email format
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                jsonResponse(['success' => false, 'message' => 'Invalid email format'], 400);
            }

            // Check if email is already taken by another user
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $stmt->execute([$email, $user['id']]);
            if ($stmt->fetch()) {
                jsonResponse(['success' => false, 'message' => 'Email already taken'], 400);
            }

            $updateFields[] = "email = ?";
            $params[] = $email;
        }

        // Handle password update
        if (isset($input['new_password']) && !empty($input['new_password'])) {
            if (!isset($input['current_password']) || empty($input['current_password'])) {
                jsonResponse(['success' => false, 'message' => 'Current password required'], 400);
            }

            // Verify current password
            $stmt = $conn->prepare("SELECT password FROM users WHERE id = ?");
            $stmt->execute([$user['id']]);
            $currentUser = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!password_verify($input['current_password'], $currentUser['password'])) {
                jsonResponse(['success' => false, 'message' => 'Current password is incorrect'], 400);
            }

            // Validate new password strength
            if (strlen($input['new_password']) < 6) {
                jsonResponse(['success' => false, 'message' => 'New password must be at least 6 characters'], 400);
            }

            $updateFields[] = "password = ?";
            $params[] = password_hash($input['new_password'], PASSWORD_DEFAULT);
        }

        if (empty($updateFields)) {
            jsonResponse(['success' => false, 'message' => 'No fields to update'], 400);
        }

        // Add updated_at timestamp
        $updateFields[] = "updated_at = NOW()";
        $params[] = $user['id'];

        // Update user
        $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);

        // Get updated user data
        $stmt = $conn->prepare("SELECT id, username, email, role, created_at, updated_at FROM users WHERE id = ?");
        $stmt->execute([$user['id']]);
        $updatedUser = $stmt->fetch(PDO::FETCH_ASSOC);

        jsonResponse([
            'success' => true,
            'message' => 'Profile updated successfully',
            'data' => ['user' => $updatedUser]
        ]);

    } catch (PDOException $e) {
        jsonResponse(['success' => false, 'message' => 'Database error: ' . $e->getMessage()], 500);
    }
}

function requireAuth() {
    $headers = getallheaders();
    $token = null;

    if (isset($headers['Authorization'])) {
        $authHeader = $headers['Authorization'];
        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            $token = $matches[1];
        }
    }

    if (!$token) {
        jsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
    }

    $user = verifyToken($token);
    if (!$user) {
        jsonResponse(['success' => false, 'message' => 'Invalid or expired token'], 401);
    }

    return $user;
}
?>
